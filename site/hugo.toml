baseURL = "/"
title = "Envoy Gateway"

# Language settings
contentDir = "content"
defaultContentLanguage = "en"
defaultContentLanguageInSubdir = false
# Useful when translating.
enableMissingTranslationPlaceholders = true

enableRobotsTXT = false

# Will give values to .Lastmod etc.
enableGitInfo = true

# Comment out to enable taxonomies in Docsy
# disableKinds = ["taxonomy", "taxonomyTerm"]

# You can add your own taxonomies
[taxonomies]
tag = "tags"
category = "categories"

[params.taxonomy]
# set taxonomyCloud = [] to hide taxonomy clouds
taxonomyCloud = ["tags", "categories"]

# If used, must have same length as taxonomyCloud
taxonomyCloudTitle = ["Tag Cloud", "Categories"]

# set taxonomyPageHeader = [] to hide taxonomies on the page headers
taxonomyPageHeader = ["tags", "categories"]

# Highlighting config
pygmentsCodeFences = true
pygmentsUseClasses = false
# Use the new Chroma Go highlighter in Hugo.
pygmentsUseClassic = false
#pygmentsOptions = "linenos=table"
# See https://help.farbox.com/pygments.html
pygmentsStyle = "tango"

# Configure how URLs look like per section.
[permalinks]
blog = "/:section/:year/:month/:day/:slug/"

# Image processing configuration.
[imaging]
resampleFilter = "CatmullRom"
quality = 75
anchor = "smart"

[services]
[services.googleAnalytics]
# Comment out the next line to disable GA tracking. Also disables the feature described in [params.ui.feedback].
id = "G-DXJEH1ZRXX"

# Language configuration

[languages]
  [languages.en]
    contentDir = "content/en"
    languageName ="English"
    # Weight used for sorting.
    weight = 1
  [languages.en.params]
    title = "Envoy Gateway"
    description = "Manages Envoy Proxy as a Standalone or Kubernetes-based API Gateway"

[markup]
  [markup.goldmark]
    [markup.goldmark.parser.attribute]
      block = true
    [markup.goldmark.renderer]
      unsafe = true
    [markup.goldmark.renderHooks]
      [markup.goldmark.renderHooks.image]
        enableDefault = true
  [markup.highlight]
    # See a complete list of available styles at https://xyproto.github.io/splash/docs/all.html
    style = "tango"
    # Uncomment if you want your chosen highlight style used for code blocks without a specified language
    # guessSyntax = "true"

# Everything below this are Site Params

# Comment out if you don't want the "print entire section" link enabled.
[outputs]
section = ["HTML", "print", "RSS"]

[params]
copyright = "The Envoy Gateway Authors"


# First one is picked as the Twitter card image if not set on page, as well as the open graph image.
images = ["/img/envoy-gateway-feature.png"]

# Menu title if your navbar has a versions selector to access old versions of your site.
# This menu appears only if you have at least one [params.versions] set.
version_menu = "Versions"

# Flag used in the "version-banner" partial to decide whether to display a
# banner on every page indicating that this is an archived version of the docs.
# Set this flag to "true" if you want to display the banner.
archived_version = false

# The version number for the version of the docs represented in this doc set.
# Used in the "version-banner" partial to display a version number for the
# current doc set.
version = "v1.4"

# A link to latest version of the docs. Used in the "version-banner" partial to
# point people to the main doc site.
url_latest_version = "/docs"

# Repository configuration (URLs for in-page links to opening issues and suggesting changes)
github_repo = "https://github.com/envoyproxy/gateway"
# An optional link to a related project repo. For example, the sibling repository where your product code lives.
github_project_repo = "https://github.com/envoyproxy/gateway"

# Specify a value here if your content directory is not in your repo's root directory
github_subdir = "/site/"

# Uncomment this if your GitHub repo does not have "main" as the default branch,
# or specify a new value if you want to reference another branch in your GitHub links
github_branch= "main"

# Google Custom Search Engine ID. Remove or comment out to disable search.
# gcs_engine_id = "d72aa9b2712488cc3"

# Enable Lunr.js offline search
offlineSearch = true

# Enable syntax highlighting and copy buttons on code blocks with Prism
prism_syntax_highlighting = false

# User interface configuration
[params.ui]
#  Set to true to disable breadcrumb navigation.
breadcrumb_disable = false
# Set to true to disable the About link in the site footer
footer_about_enable = true
# Set to false if you don't want to display a logo (/assets/icons/logo.svg) in the top navbar
navbar_logo = true
# Set to true if you don't want the top navbar to be translucent when over a `block/cover`, like on the homepage.
navbar_translucent_over_cover_disable = true
# Enable to show the side bar menu in its compact state.
sidebar_menu_compact = true
# Set to true to hide the sidebar search box (the top nav search box will still be displayed if search is enabled)
sidebar_search_disable = false
# Show expand/collapse icon for sidebar sections
sidebar_menu_foldable = true

# Adds a H2 section titled "Feedback" to the bottom of each doc. The responses are sent to Google Analytics as events.
# This feature depends on [services.googleAnalytics] and will be disabled if "services.googleAnalytics.id" is not set.
# If you want this feature, but occasionally need to remove the "Feedback" section from a single page,
# add "hide_feedback: true" to the page's front matter.
[params.ui.feedback]
enable = true
# The responses that the user sees after clicking "yes" (the page was helpful) or "no" (the page was not helpful).
yes = 'Glad to hear it! Please <a href="https://github.com/envoyproxy/gateway/issues/new">tell us how we can improve</a>.'
no = 'Sorry to hear that. Please <a href="https://github.com/envoyproxy/gateway/issues/new">tell us how we can improve</a>.'

# Adds a reading time to the top of each doc.
# If you want this feature, but occasionally need to remove the Reading time from a single page,
# add "hide_readingtime: true" to the page's front matter
[params.ui.readingtime]
enable = true

# hugo module configuration

[module]
  # uncomment line below for temporary local development of module
  # replacements = "github.com/envoyproxy/gateway -> ../../docsy"
  [module.hugoVersion]
    extended = true
    min = "0.110.0"
  [[module.imports]]
    path = "github.com/google/docsy"
    disable = false
  [[module.imports]]
    path = "github.com/google/docsy/dependencies"
    disable = false

[params.links]
  # End user relevant links. These will show up on left side of footer and in the community page if you have one.
  [[params.links.user]]
    name = "User mailing list"
    url = "mailto:<EMAIL>"
    icon = "fa fa-envelope"
    desc = "Discussion and help from your fellow users"
  [[params.links.user]]
    name ="Twitter"
    url = "https://twitter.com/EnvoyProxy"
    icon = "fab fa-twitter"
    desc = "Follow us on Twitter to get the latest news!"
  # Developer relevant links. These will show up on right side of footer and in the community page if you have one.
  [[params.links.developer]]
    name = "GitHub"
    url = "https://github.com/envoyproxy/gateway"
    icon = "fab fa-github"
    desc = "Development takes place here!"
  [[params.links.developer]]
    name = "Slack"
    url = "https://envoyproxy.slack.com/archives/C03E6NHLESV"
    icon = "fab fa-slack"
    desc = "Chat with other project developers"

[menu]
  [[menu.main]]
    name = "Contributions"
    weight = -98
    url = "/contributions"
  [[menu.main]]
    name = "About"
    weight = -100
    url = "/about"
  [[menu.main]]
    name = "News"
    weight = -101
    url = "/news"
  [[menu.main]]
    name = "Documentation"
    weight = -102
    url = "/docs"

[[params.versions]]
  version = "latest"
  url = "/latest"
  eol = "2099-12-31"

[[params.versions]]
  version = "v1.4"
  url = "/v1.4"
  eol = "2025-11-13"

[[params.versions]]
  version = "v1.3"
  url = "/v1.3"
  eol = "2025-07-30"

[[params.versions]]
  version = "v1.2"
  url = "/v1.2"
  eol = "2025-05-06"

[[params.versions]]
  version = "v1.1"
  url = "/v1.1"
  eol = "2025-01-22"

[[params.versions]]
  version = "v1.0"
  url = "/v1.0"
  eol = "2024-09-13"

[[params.versions]]
  version = "v0.6"
  url = "/v0.6"
  eol = "2024-05-02"

[[params.versions]]
  version = "v0.5"
  url = "/v0.5"
  eol = "2024-01-02"

[[params.versions]]
  version = "v0.4"
  url = "/v0.4"
  eol = "2023-10-24"

[[params.versions]]
  version = "v0.3"
  url = "/v0.3"
  eol = "2023-08-09"

[[params.versions]]
  version = "v0.2"
  url = "/v0.2"
  eol = "2023-04-20"
