apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  creationTimestamp: null
  labels:
    gateway.envoyproxy.io/owning-gateway-name: gateway-1
    gateway.envoyproxy.io/owning-gateway-namespace: namespace-1
  name: gateway-1
  namespace: namespace-1
  ownerReferences:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    name: gateway-1
    uid: test-owner-reference-uid-for-gateway
spec:
  maxReplicas: 3
  metrics:
  - resource:
      name: cpu
      target:
        averageUtilization: 80
        type: Utilization
    type: Resource
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: gateway-1
status:
  currentMetrics: null
  desiredReplicas: 0
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  creationTimestamp: null
  labels:
    gateway.envoyproxy.io/owning-gateway-name: gateway-2
    gateway.envoyproxy.io/owning-gateway-namespace: namespace-2
  name: gateway-2
  namespace: namespace-2
  ownerReferences:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    name: gateway-2
    uid: test-owner-reference-uid-for-gateway
spec:
  maxReplicas: 3
  metrics:
  - resource:
      name: cpu
      target:
        averageUtilization: 80
        type: Utilization
    type: Resource
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: gateway-2
status:
  currentMetrics: null
  desiredReplicas: 0
