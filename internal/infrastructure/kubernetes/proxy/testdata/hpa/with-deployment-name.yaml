apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  creationTimestamp: null
  labels:
    gateway.envoyproxy.io/owning-gateway-name: default
    gateway.envoyproxy.io/owning-gateway-namespace: default
  name: envoy-default-37a8eec1
  namespace: envoy-gateway-system
  ownerReferences:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: GatewayClass
    name: envoy-gateway-class
    uid: test-owner-reference-uid-for-gatewayclass
spec:
  maxReplicas: 10
  metrics:
  - resource:
      name: cpu
      target:
        averageUtilization: 80
        type: Utilization
    type: Resource
  minReplicas: 5
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: custom-deployment-name
status:
  currentMetrics: null
  desiredReplicas: 0
