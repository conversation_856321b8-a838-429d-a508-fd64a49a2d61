clientTrafficPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: ClientTrafficPolicy
  metadata:
    namespace: envoy-gateway
    name: target-gateway-1
  spec:
    connection: {}
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: ClientTrafficPolicy
  metadata:
    namespace: envoy-gateway
    name: target-gateway-1-section-http-1
  spec:
    connection:
      bufferLimit: 50M
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
      sectionName: http-1
gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-1
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http-1
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: Same
    - name: http-2
      protocol: HTTP
      port: 8080
      allowedRoutes:
        namespaces:
          from: Same
