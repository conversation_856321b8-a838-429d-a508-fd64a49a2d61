secrets:
  - apiVersion: v1
    kind: Secret
    metadata:
      namespace: default
      name: users-secret1
    data:
      .htpasswd: "dXNlcjE6e1NIQX10RVNzQm1FL3lOWTNsYjZhMEw2dlZRRVpOcXc9CnVzZXIyOntTSEF9RUo5TFBGRFhzTjl5blNtYnh2anA3NUJtbHg4PQo="
  - apiVersion: v1
    kind: Secret
    metadata:
      namespace: default
      name: users-secret2
    data:
      .htpasswd: "Zm9vOntTSEF9WXMyM0FnLzVJT1dxWkN3OVFHYVZEZEh3SDAwPQpmb28xOntTSEF9ZGpaMTFxSFkwS09pamV5bUs3YUt2WXV2aHZNPQo="
gateways:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    metadata:
      namespace: default
      name: gateway-1
    spec:
      gatewayClassName: envoy-gateway-class
      listeners:
        - name: http
          protocol: HTTP
          port: 80
          allowedRoutes:
            namespaces:
              from: All
httpRoutes:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: HTTPRoute
    metadata:
      namespace: default
      name: httproute-1
    spec:
      hostnames:
        - www.foo.com
      parentRefs:
        - namespace: default
          name: gateway-1
          sectionName: http
      rules:
        - matches:
            - path:
                value: /foo1
          backendRefs:
            - name: service-1
              port: 8080
        - matches:
            - path:
                value: /foo2
          backendRefs:
            - name: service-2
              port: 8080
  - apiVersion: gateway.networking.k8s.io/v1
    kind: HTTPRoute
    metadata:
      namespace: default
      name: httproute-2
    spec:
      hostnames:
        - www.bar.com
      parentRefs:
        - namespace: default
          name: gateway-1
          sectionName: http
      rules:
        - matches:
            - path:
                value: /bar
          backendRefs:
            - name: service-3
              port: 8080
securityPolicies:
  - apiVersion: gateway.envoyproxy.io/v1alpha1
    kind: SecurityPolicy
    metadata:
      namespace: default
      name: policy-for-http-route-1
    spec:
      targetRef:
        group: gateway.networking.k8s.io
        kind: HTTPRoute
        name: httproute-1
      basicAuth:
        users:
          name: "users-secret1"
  - apiVersion: gateway.envoyproxy.io/v1alpha1
    kind: SecurityPolicy
    metadata:
      namespace: default
      name: policy-for-gateway-1               # This will only apply to the httproute-2
    spec:
      targetRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
      basicAuth:
        users:
          name: "users-secret2"
