gateways:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    metadata:
      name: gateway-btls
      namespace: envoy-gateway
    spec:
      gatewayClassName: envoy-gateway-class
      listeners:
        - name: http
          protocol: HTTP
          port: 80
          allowedRoutes:
            namespaces:
              from: All
httpRoutes:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: HTTPRoute
    metadata:
      name: httproute-btls
      namespace: envoy-gateway
    spec:
      parentRefs:
        - namespace: envoy-gateway
          name: gateway-btls
          sectionName: http
      rules:
        - matches:
            - path:
                type: Exact
                value: "/exact"
          backendRefs:
            - name: http-backend
              namespace: backends
              port: 8080

referenceGrants:
  - apiVersion: gateway.networking.k8s.io/v1alpha2
    kind: ReferenceGrant
    metadata:
      name: refg-route-svc
      namespace: backends
    spec:
      from:
        - group: gateway.networking.k8s.io
          kind: HTTPRoute
          namespace: envoy-gateway
        - group: gateway.networking.k8s.io
          kind: Gateway
          namespace: envoy-gateway
      to:
        - group: ""
          kind: Service

services:
  - apiVersion: v1
    kind: Service
    metadata:
      name: http-backend
      namespace: backends
    spec:
      clusterIP: ***********
      ports:
        - port: 8080
          name: http
          protocol: TCP
          targetPort: 8080

endpointSlices:
  - apiVersion: discovery.k8s.io/v1
    kind: EndpointSlice
    metadata:
      name: endpointslice-http-backend
      namespace: backends
      labels:
        kubernetes.io/service-name: http-backend
    addressType: IPv4
    ports:
      - name: http
        protocol: TCP
        port: 8080
    endpoints:
      - addresses:
          - "***********"
        conditions:
          ready: true

configMaps:
  - apiVersion: v1
    kind: ConfigMap
    metadata:
      name: no-ca-cmap
      namespace: backends
    data:
      garbage.crt: |
        itsAllGarbage
backendTLSPolicies:
  - apiVersion: gateway.networking.k8s.io/v1alpha2
    kind: BackendTLSPolicy
    metadata:
      name: policy-btls
      namespace: backends
    spec:
      targetRefs:
        - group: ""
          kind: Service
          name: http-backend
          sectionName: http
      validation:
        caCertificateRefs:
          - name: no-ca-cmap
            group: ""
            kind: ConfigMap
        hostname: example.com
