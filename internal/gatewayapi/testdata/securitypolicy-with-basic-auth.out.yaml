gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: default
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 2
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-1
    namespace: default
  spec:
    hostnames:
    - www.foo.com
    parentRefs:
    - name: gateway-1
      namespace: default
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - path:
          value: /foo1
    - backendRefs:
      - name: service-2
        port: 8080
      matches:
      - path:
          value: /foo2
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: default
        sectionName: http
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    creationTimestamp: null
    name: httproute-2
    namespace: default
  spec:
    hostnames:
    - www.bar.com
    parentRefs:
    - name: gateway-1
      namespace: default
      sectionName: http
    rules:
    - backendRefs:
      - name: service-3
        port: 8080
      matches:
      - path:
          value: /bar
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: default
        sectionName: http
infraIR:
  default/gateway-1:
    proxy:
      listeners:
      - address: null
        name: default/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: default
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: default/gateway-1
      namespace: envoy-gateway-system
securityPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: SecurityPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-http-route-1
    namespace: default
  spec:
    basicAuth:
      users:
        group: null
        kind: null
        name: users-secret1
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: default
        sectionName: http
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: SecurityPolicy
  metadata:
    creationTimestamp: null
    name: policy-for-gateway-1
    namespace: default
  spec:
    basicAuth:
      users:
        group: null
        kind: null
        name: users-secret2
    targetRef:
      group: gateway.networking.k8s.io
      kind: Gateway
      name: gateway-1
  status:
    ancestors:
    - ancestorRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
        namespace: default
      conditions:
      - lastTransitionTime: null
        message: Policy has been accepted.
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: 'This policy is being overridden by other securityPolicies for these
          routes: [default/httproute-1]'
        reason: Overridden
        status: "True"
        type: Overridden
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
xdsIR:
  default/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: default
        sectionName: http
      name: default/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: default
          name: httproute/default/httproute-1/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-1/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: www.foo.com
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/0/match/0/www_foo_com
        pathMatch:
          distinct: false
          name: ""
          prefix: /foo1
        security:
          basicAuth:
            name: securitypolicy/default/policy-for-http-route-1
            users: '[redacted]'
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-1
            namespace: default
          name: httproute/default/httproute-1/rule/1
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-2
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-1/rule/1/backend/0
            protocol: HTTP
            weight: 1
        hostname: www.foo.com
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-1
          namespace: default
        name: httproute/default/httproute-1/rule/1/match/0/www_foo_com
        pathMatch:
          distinct: false
          name: ""
          prefix: /foo2
        security:
          basicAuth:
            name: securitypolicy/default/policy-for-http-route-1
            users: '[redacted]'
      - destination:
          metadata:
            kind: HTTPRoute
            name: httproute-2
            namespace: default
          name: httproute/default/httproute-2/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
            metadata:
              name: service-3
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-2/rule/0/backend/0
            protocol: HTTP
            weight: 1
        hostname: www.bar.com
        isHTTP2: false
        metadata:
          kind: HTTPRoute
          name: httproute-2
          namespace: default
        name: httproute/default/httproute-2/rule/0/match/0/www_bar_com
        pathMatch:
          distinct: false
          name: ""
          prefix: /bar
        security:
          basicAuth:
            name: securitypolicy/default/policy-for-gateway-1
            users: '[redacted]'
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
