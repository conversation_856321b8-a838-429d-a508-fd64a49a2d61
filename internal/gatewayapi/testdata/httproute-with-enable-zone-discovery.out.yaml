gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: All
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 1
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    annotations:
      gateway.envoyproxy.io/foo: bar
      k8s.io/bar: foo
    creationTimestamp: null
    name: httproute-1
    namespace: default
  spec:
    parentRefs:
    - name: gateway-1
      namespace: envoy-gateway
      sectionName: http
    rules:
    - backendRefs:
      - name: service-1
        port: 8080
      name: rule-1
    - backendRefs:
      - name: service-1
        port: 8080
      matches:
      - headers:
        - name: foo
          type: Exact
          value: bar
      name: rule-2
  status:
    parents:
    - conditions:
      - lastTransitionTime: null
        message: Route is accepted
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Resolved all the Object references for the Route
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
      parentRef:
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      config:
        apiVersion: gateway.envoyproxy.io/v1alpha1
        kind: EnvoyProxy
        metadata:
          creationTimestamp: null
          name: test
          namespace: envoy-gateway-system
        spec:
          logging: {}
        status: {}
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: envoy-gateway/gateway-1
      namespace: envoy-gateway-system
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
      routes:
      - destination:
          metadata:
            annotations:
              foo: bar
            kind: HTTPRoute
            name: httproute-1
            namespace: default
            sectionName: rule-2
          name: httproute/default/httproute-1/rule/1
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
              zone: antarctica-east1b
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-1/rule/1/backend/0
            protocol: HTTP
            weight: 1
            zoneAwareRoutingEnabled: true
        headerMatches:
        - distinct: false
          exact: bar
          name: foo
        hostname: '*'
        isHTTP2: false
        metadata:
          annotations:
            foo: bar
          kind: HTTPRoute
          name: httproute-1
          namespace: default
          sectionName: rule-2
        name: httproute/default/httproute-1/rule/1/match/0/*
      - destination:
          metadata:
            annotations:
              foo: bar
            kind: HTTPRoute
            name: httproute-1
            namespace: default
            sectionName: rule-1
          name: httproute/default/httproute-1/rule/0
          settings:
          - addressType: IP
            endpoints:
            - host: *******
              port: 8080
              zone: antarctica-east1b
            metadata:
              name: service-1
              namespace: default
              sectionName: "8080"
            name: httproute/default/httproute-1/rule/0/backend/0
            protocol: HTTP
            weight: 1
            zoneAwareRoutingEnabled: true
        hostname: '*'
        isHTTP2: false
        metadata:
          annotations:
            foo: bar
          kind: HTTPRoute
          name: httproute-1
          namespace: default
          sectionName: rule-1
        name: httproute/default/httproute-1/rule/0/match/-1/*
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
