gateways:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    metadata:
      namespace: envoy-gateway
      name: gateway-1
    spec:
      gatewayClassName: envoy-gateway-class
      listeners:
        - name: http
          protocol: HTTP
          port: 80
          allowedRoutes:
            namespaces:
              from: All
  - apiVersion: gateway.networking.k8s.io/v1
    kind: Gateway
    metadata:
      namespace: envoy-gateway
      name: gateway-2
    spec:
      gatewayClassName: envoy-gateway-class
      listeners:
        - name: http
          protocol: HTTP
          port: 80
          allowedRoutes:
            namespaces:
              from: All
grpcRoutes:
  - apiVersion: gateway.networking.k8s.io/v1alpha2
    kind: GRPCRoute
    metadata:
      namespace: default
      name: grpcroute-1
    spec:
      parentRefs:
        - namespace: envoy-gateway
          name: gateway-1
          sectionName: http
      rules:
        - backendRefs:
            - name: service-1
              port: 8080
httpRoutes:
  - apiVersion: gateway.networking.k8s.io/v1
    kind: HTTPRoute
    metadata:
      namespace: default
      name: httproute-1
    spec:
      hostnames:
        - gateway.envoyproxy.io
      parentRefs:
        - namespace: envoy-gateway
          name: gateway-2
          sectionName: http
      rules:
        - matches:
            - path:
                value: "/"
          backendRefs:
            - name: service-1
              port: 8080
backendTrafficPolicies:
  - apiVersion: gateway.envoyproxy.io/v1alpha1
    kind: BackendTrafficPolicy
    metadata:
      namespace: envoy-gateway
      name: policy-for-gateway
    spec:
      targetRef:
        group: gateway.networking.k8s.io
        kind: Gateway
        name: gateway-1
      timeout:
        tcp:
          connectTimeout: 15s
        http:
          connectionIdleTimeout: 16s
          maxConnectionDuration: 17s
  - apiVersion: gateway.envoyproxy.io/v1alpha1
    kind: BackendTrafficPolicy
    metadata:
      namespace: default
      name: policy-for-route
    spec:
      targetRef:
        group: gateway.networking.k8s.io
        kind: HTTPRoute
        name: httproute-1
      connection:
        bufferLimit: 100M
