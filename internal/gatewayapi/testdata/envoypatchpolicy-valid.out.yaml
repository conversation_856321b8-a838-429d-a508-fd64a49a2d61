gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    creationTimestamp: null
    name: gateway-1
    namespace: envoy-gateway
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - allowedRoutes:
        namespaces:
          from: Same
      name: http
      port: 80
      protocol: HTTP
  status:
    listeners:
    - attachedRoutes: 0
      conditions:
      - lastTransitionTime: null
        message: Sending translated listener configuration to the data plane
        reason: Programmed
        status: "True"
        type: Programmed
      - lastTransitionTime: null
        message: Listener has been successfully translated
        reason: Accepted
        status: "True"
        type: Accepted
      - lastTransitionTime: null
        message: Listener references have been resolved
        reason: ResolvedRefs
        status: "True"
        type: ResolvedRefs
      name: http
      supportedKinds:
      - group: gateway.networking.k8s.io
        kind: HTTPRoute
      - group: gateway.networking.k8s.io
        kind: GRPCRoute
infraIR:
  envoy-gateway/gateway-1:
    proxy:
      listeners:
      - address: null
        name: envoy-gateway/gateway-1/http
        ports:
        - containerPort: 10080
          name: http-80
          protocol: HTTP
          servicePort: 80
      metadata:
        labels:
          gateway.envoyproxy.io/owning-gateway-name: gateway-1
          gateway.envoyproxy.io/owning-gateway-namespace: envoy-gateway
        ownerReference:
          kind: GatewayClass
          name: envoy-gateway-class
      name: envoy-gateway/gateway-1
      namespace: envoy-gateway-system
xdsIR:
  envoy-gateway/gateway-1:
    accessLog:
      json:
      - path: /dev/stdout
    envoyPatchPolicies:
    - jsonPatches:
      - name: envoy-gateway-gateway-1-http
        operation:
          op: replace
          path: /ignore_global_conn_limit
          value: "true"
        type: type.googleapis.com/envoy.config.listener.v3.Listener
      name: edit-ignore-global-limit
      namespace: envoy-gateway
      status:
        ancestors:
        - ancestorRef:
            group: gateway.networking.k8s.io
            kind: Gateway
            name: gateway-1
            namespace: envoy-gateway
          conditions:
          - lastTransitionTime: null
            message: Policy has been accepted.
            reason: Accepted
            status: "True"
            type: Accepted
          controllerName: gateway.envoyproxy.io/gatewayclass-controller
    - jsonPatches:
      - name: envoy-gateway-gateway-1-http
        operation:
          op: replace
          path: /per_connection_buffer_limit_bytes
          value: "1024"
        type: type.googleapis.com/envoy.config.listener.v3.Listener
      name: edit-conn-buffer-bytes
      namespace: envoy-gateway
      status:
        ancestors:
        - ancestorRef:
            group: gateway.networking.k8s.io
            kind: Gateway
            name: gateway-1
            namespace: envoy-gateway
          conditions:
          - lastTransitionTime: null
            message: Policy has been accepted.
            reason: Accepted
            status: "True"
            type: Accepted
          controllerName: gateway.envoyproxy.io/gatewayclass-controller
    http:
    - address: 0.0.0.0
      hostnames:
      - '*'
      isHTTP2: false
      metadata:
        kind: Gateway
        name: gateway-1
        namespace: envoy-gateway
        sectionName: http
      name: envoy-gateway/gateway-1/http
      path:
        escapedSlashesAction: UnescapeAndRedirect
        mergeSlashes: true
      port: 10080
    readyListener:
      address: 0.0.0.0
      ipFamily: IPv4
      path: /ready
      port: 19003
