- clusterName: httproute/default/httproute-1/rule/0
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 8080
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: httproute/default/httproute-1/rule/0/backend/0
- clusterName: httproute/default/httproute-2/rule/0
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 8080
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: httproute/default/httproute-2/rule/0/backend/0
- clusterName: envoyextensionpolicy/default/policy-for-route-2/0/grpc-backend-4
  endpoints:
  - loadBalancingWeight: 1
    locality:
      region: envoyextensionpolicy/default/policy-for-route-2/0/grpc-backend-4/backend/0
- clusterName: envoyextensionpolicy/default/policy-for-route-1/0/grpc-backend-2
  endpoints:
  - loadBalancingWeight: 1
    locality:
      region: envoyextensionpolicy/default/policy-for-route-1/0/grpc-backend-2/backend/0
- clusterName: envoyextensionpolicy/envoy-gateway/policy-for-gateway-2/0/grpc-backend-3
  endpoints:
  - loadBalancingWeight: 1
    locality:
      region: envoyextensionpolicy/envoy-gateway/policy-for-gateway-2/0/grpc-backend-3/backend/0
- clusterName: envoyextensionpolicy/envoy-gateway/policy-for-gateway-1/0/grpc-backend
  endpoints:
  - loadBalancingWeight: 1
    locality:
      region: envoyextensionpolicy/envoy-gateway/policy-for-gateway-1/0/grpc-backend/backend/0
