- ignorePortInHostMatching: true
  name: envoy-gateway/gateway-1/http
  virtualHosts:
  - domains:
    - gateway.envoyproxy.io
    name: envoy-gateway/gateway-1/http/gateway_envoyproxy_io
    routes:
    - match:
        pathSeparatedPrefix: /foo
      name: httproute/default/httproute-1/rule/0/match/0/gateway_envoyproxy_io
      route:
        cluster: httproute/default/httproute-1/rule/0
        upgradeConfigs:
        - upgradeType: websocket
      typedPerFilterConfig:
        envoy.filters.http.ext_proc/envoyextensionpolicy/default/policy-for-route-1/extproc/0:
          '@type': type.googleapis.com/envoy.config.route.v3.FilterConfig
          config: {}
        envoy.filters.http.ext_proc/envoyextensionpolicy/default/policy-for-route-2/extproc/0:
          '@type': type.googleapis.com/envoy.config.route.v3.FilterConfig
          config: {}
    - match:
        pathSeparatedPrefix: /bar
      name: httproute/default/httproute-2/rule/0/match/0/gateway_envoyproxy_io
      route:
        cluster: httproute/default/httproute-2/rule/0
        upgradeConfigs:
        - upgradeType: websocket
      typedPerFilterConfig:
        envoy.filters.http.ext_proc/envoyextensionpolicy/envoy-gateway/policy-for-gateway-1/extproc/0:
          '@type': type.googleapis.com/envoy.config.route.v3.FilterConfig
          config: {}
        envoy.filters.http.ext_proc/envoyextensionpolicy/envoy-gateway/policy-for-gateway-2/extproc/0:
          '@type': type.googleapis.com/envoy.config.route.v3.FilterConfig
          config: {}
