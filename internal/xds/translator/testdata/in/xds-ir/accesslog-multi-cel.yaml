name: "accesslog"
accesslog:

  text:
  - path: "/dev/stdout"
    celMatches:
    - response.code >= 400
    - request.url_path.contains('v1beta3')
    format: |
      [%START_TIME%] "%REQ(:METHOD)% %REQ(X-ENVOY-ORIGINAL-PATH?:PATH)% %PROTOCOL%" %RESPONSE_CODE% %RESPONSE_FLAGS% %BYTES_RECEIVED% %BYTES_SENT% %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% "%REQ(X-FORWARDED-FOR)%" "%REQ(USER-AGENT)%" "%REQ(X-REQUEST-ID)%" "%REQ(:AUTHORITY)%" "%UPSTREAM_HOST%"
  json:
  - path: "/dev/stdout"
    celMatches:
    - response.code >= 400
    - request.url_path.contains('v1beta3')
    json:
      start_time: "%START_TIME%"
      method: "%REQ(:METHOD)%"
      path: "%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%"
      protocol: "%PROTOCOL%"
      response_code: "%RESPONSE_CODE%"
  openTelemetry:
  - text: |
      [%START_TIME%] "%REQ(:METHOD)% %REQ(X-ENVOY-ORIGINAL-PATH?:PATH)% %PROTOCOL%" %RESPONSE_CODE% %RESPONSE_FLAGS% %BYTES_RECEIVED% %BYTES_SENT% %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% "%REQ(X-FORWARDED-FOR)%" "%REQ(USER-AGENT)%" "%REQ(X-REQUEST-ID)%" "%REQ(:AUTHORITY)%" "%UPSTREAM_HOST%"
    attributes:
      "response_code": "%RESPONSE_CODE%"
    resources:
      "cluster_name": "cluster1"
    authority: "otel-collector.default.svc.cluster.local"
    celMatches:
    - response.code >= 400
    - request.url_path.contains('v1beta3')
    destination:
      name: "accesslog-0"
      settings:
      - endpoints:
        - host: "otel-collector.default.svc.cluster.local"
          port: 4317
        protocol: "GRPC"
        name: "accesslog-0/backend/0"
http:
- name: "first-listener"
  address: "::"
  port: 10080
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  routes:
  - name: "direct-route"
    hostname: "*"
    destination:
      name: "direct-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "direct-route-dest/backend/0"
