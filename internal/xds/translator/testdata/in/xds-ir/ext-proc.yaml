http:
  - address: 0.0.0.0
    hostnames:
      - '*'
    isHTTP2: false
    name: envoy-gateway/gateway-1/http
    path:
      escapedSlashesAction: UnescapeAndRedirect
      mergeSlashes: true
    port: 10080
    routes:
      - destination:
          name: httproute/default/httproute-1/rule/0
          settings:
            - addressType: IP
              endpoints:
                - host: *******
                  port: 8080
              protocol: HTTP
              weight: 1
              name: httproute/default/httproute-1/rule/0/backend/0
        envoyExtensions:
          extProcs:
            - name: envoyextensionpolicy/default/policy-for-route-2/extproc/0
              failOpen: true
              messageTimeout: 5s
              requestAttributes:
                - xds.route_metadata
                - connection.requested_server_name
              requestHeaderProcessing: true
              requestBodyProcessingMode: Buffered
              responseAttributes:
                - request.path
              responseBodyProcessingMode: Streamed
              allowModeOverride: true
              authority: grpc-backend-4.default:4000
              forwardingMetadataNamespaces:
                - envoy.filters.http.ext_authz
              receivingMetadataNamespaces:
                - envoy.filters.http.my_custom
              destination:
                name: envoyextensionpolicy/default/policy-for-route-2/0/grpc-backend-4
                settings:
                  - protocol: GRPC
                    weight: 1
                    name: envoyextensionpolicy/default/policy-for-route-2/0/grpc-backend-4/backend/0
            - name: envoyextensionpolicy/default/policy-for-route-1/extproc/0
              failOpen: true
              messageTimeout: 5s
              responseHeaderProcessing: true
              requestBodyProcessingMode: BufferedPartial
              authority: grpc-backend-2.default:8000
              destination:
                name: envoyextensionpolicy/default/policy-for-route-1/0/grpc-backend-2
                settings:
                  - protocol: GRPC
                    weight: 1
                    name: envoyextensionpolicy/default/policy-for-route-1/0/grpc-backend-2/backend/0
        hostname: gateway.envoyproxy.io
        isHTTP2: false
        name: httproute/default/httproute-1/rule/0/match/0/gateway_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /foo
      - destination:
          name: httproute/default/httproute-2/rule/0
          settings:
            - addressType: IP
              endpoints:
                - host: *******
                  port: 8080
              protocol: HTTP
              weight: 1
              name: httproute/default/httproute-2/rule/0/backend/0
        envoyExtensions:
          extProcs:
            - name: envoyextensionpolicy/envoy-gateway/policy-for-gateway-2/extproc/0
              authority: grpc-backend-3.envoy-gateway:3000
              destination:
                name: envoyextensionpolicy/envoy-gateway/policy-for-gateway-2/0/grpc-backend-3
                settings:
                  - protocol: GRPC
                    weight: 1
                    name: envoyextensionpolicy/envoy-gateway/policy-for-gateway-2/0/grpc-backend-3/backend/0
            - name: envoyextensionpolicy/envoy-gateway/policy-for-gateway-1/extproc/0
              failOpen: false
              messageTimeout: 15s
              requestAttributes:
                - xds.route_metadata
                - connection.requested_server_name
              responseAttributes:
                - request.path
              forwardingMetadataNamespaces:
                - envoy.filters.http.ext_proc
              receivingMetadataNamespaces:
                - envoy.filters.http.prc_ext
              authority: grpc-backend.envoy-gateway:9000
              destination:
                name: envoyextensionpolicy/envoy-gateway/policy-for-gateway-1/0/grpc-backend
                settings:
                  - protocol: GRPC
                    weight: 1
                    name: envoyextensionpolicy/envoy-gateway/policy-for-gateway-1/0/grpc-backend/backend/0
        hostname: gateway.envoyproxy.io
        isHTTP2: false
        name: httproute/default/httproute-2/rule/0/match/0/gateway_envoyproxy_io
        pathMatch:
          distinct: false
          name: ""
          prefix: /bar
