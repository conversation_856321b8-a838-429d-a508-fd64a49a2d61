name: Latest Release

permissions:
  contents: read

on:
  push:
    branches:
    - "main"
    paths-ignore:
    - "**/*.png"

# Limit workflow run or job concurrency,
# avoid workflow failed when merge more than one PR in short time.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # For push event, we run benchmark test here because we need to
  # include benchmark report in the latest release.
  benchmark-test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2
    - uses: ./tools/github-actions/setup-deps

    - name: Setup Graphviz
      uses: ts-graphviz/setup-graphviz@b1de5da23ed0a6d14e0aeee8ed52fdd87af2363c  # v2.0.2

    # Benchmark
    - name: Run Benchmark tests
      env:
        KIND_NODE_TAG: v1.29.10
        IMAGE_PULL_POLICY: IfNotPresent
        # Args for benchmark test
        BENCHMARK_RPS: 10000
        BENCHMARK_CONNECTIONS: 100
        BENCHMARK_DURATION: 30
        BENCHMARK_CPU_LIMITS: 1000m
        BENCHMARK_MEMORY_LIMITS: 2000Mi
        BENCHMARK_REPORT_DIR: benchmark_report
      run: make benchmark

    - name: Package benchmark report
      run: cd test/benchmark && zip -r benchmark_report.zip benchmark_report

    - name: Upload Benchmark Report
      uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02  # v4.6.2
      with:
        name: benchmark_report
        path: test/benchmark/benchmark_report.zip

  latest-release:
    runs-on: ubuntu-22.04
    needs: [benchmark-test]
    permissions:
      contents: write
    steps:
    - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2

    - name: Generate Release Manifests
      # Use `Always` image pull policy for latest version.
      run: IMAGE_PULL_POLICY=Always make generate-manifests IMAGE=envoyproxy/gateway-dev TAG=latest OUTPUT_DIR=release-artifacts

    - name: Download Benchmark Report
      uses: actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093  # v4.3.0
      with:
        name: benchmark_report
        path: release-artifacts

    - name: Build egctl latest multiarch binaries
      run: |
        make build-multiarch
        tar -zcvf envoy-gateway_latest_linux_amd64.tar.gz bin/linux/amd64/envoy-gateway
        tar -zcvf envoy-gateway_latest_linux_arm64.tar.gz bin/linux/arm64/envoy-gateway
        tar -zcvf envoy-gateway_latest_darwin_amd64.tar.gz bin/darwin/amd64/envoy-gateway
        tar -zcvf envoy-gateway_latest_darwin_arm64.tar.gz bin/darwin/arm64/envoy-gateway
        tar -zcvf egctl_latest_linux_amd64.tar.gz bin/linux/amd64/egctl
        tar -zcvf egctl_latest_linux_arm64.tar.gz bin/linux/arm64/egctl
        tar -zcvf egctl_latest_darwin_amd64.tar.gz bin/darwin/amd64/egctl
        tar -zcvf egctl_latest_darwin_arm64.tar.gz bin/darwin/arm64/egctl
        zip -r egctl_latest_windows_amd64.zip bin/windows/amd64/egctl

    # Ignore the error when we delete the latest release, it might not exist.

    # GitHub APIs take sometime to make effect, we should make sure before Recreate the Latest Release and Tag,
    # tag and release all get deleted. So we sleep sometime after deleting tag and release to wait for it taking effect.

    - name: Delete the Latest Release
      continue-on-error: true
      run: |
        gh release delete latest --repo $GITHUB_REPOSITORY
        sleep 4
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        GITHUB_REPOSITORY: ${{ github.repository_owner }}/${{ github.event.repository.name }}

    # Ignore the error when we delete the latest tag, it might not exist.
    - name: Delete the Latest Tag
      continue-on-error: true
      run: |
        gh api --method DELETE /repos/$GITHUB_REPOSITORY/git/refs/tags/latest
        sleep 4
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        GITHUB_REPOSITORY: ${{ github.repository_owner }}/${{ github.event.repository.name }}

    - name: Recreate the Latest Release and Tag
      uses: softprops/action-gh-release@da05d552573ad5aba039eaac05058a918a7bf631  # v2.2.2
      with:
        draft: false
        prerelease: true
        tag_name: latest
        files: |
          release-artifacts/install.yaml
          release-artifacts/quickstart.yaml
          release-artifacts/envoy-gateway-crds.yaml
          release-artifacts/benchmark_report.zip
          envoy-gateway_latest_linux_amd64.tar.gz
          envoy-gateway_latest_linux_arm64.tar.gz
          envoy-gateway_latest_darwin_amd64.tar.gz
          envoy-gateway_latest_darwin_arm64.tar.gz
          egctl_latest_linux_amd64.tar.gz
          egctl_latest_linux_arm64.tar.gz
          egctl_latest_darwin_amd64.tar.gz
          egctl_latest_darwin_arm64.tar.gz
          egctl_latest_windows_amd64.zip
        body: |
          This is the "latest" release of **Envoy Gateway**, which contains the most recent commits from the main branch.

          This release **might not be stable**.

          It is only intended for developers wishing to try out the latest features in Envoy Gateway, some of which may not be fully implemented.

          We use `v0.0.0-latest` as the latest chart version to install latest envoy-gateway:

          ```shell
          helm install eg oci://docker.io/envoyproxy/gateway-helm --version v0.0.0-latest -n envoy-gateway-system --create-namespace
          ```

          Try latest version of `egctl` with:

          ```shell
          curl -Ls https://gateway.envoyproxy.io/get-egctl.sh | VERSION=latest bash
          ```

    # Check if latest release and tag are created properly,
    # if not, the workflow needs to report an error,
    # let maintainers be aware of it and rerun it manually.
    # After Recreate the Latest Release and Tag, we need to sleep some time to wait
    # new tag and release are created before checking it.
    - name: Check Latest Release and Tag
      run: |
        sleep 4
        make latest-release-check
