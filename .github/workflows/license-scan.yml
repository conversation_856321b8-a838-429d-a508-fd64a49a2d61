name: License Scan

on:
  pull_request:
    branches:
    - "main"
  push:
    branches:
    - "main"

permissions:
  contents: read

jobs:
  scan:
    runs-on: ubuntu-22.04
    steps:
    - name: Checkout code
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683  # v4.2.2
    - name: Run scanner
      uses: google/osv-scanner-action/osv-scanner-action@e69cc6c86b31f1e7e23935bbe7031b50e51082de  # v2.0.2
      continue-on-error: true  # remove this after https://github.com/google/deps.dev/issues/146 has been resolved
      with:
        scan-args: |-
          --licenses=Apache-2.0,BSD-2-<PERSON><PERSON>,BSD-2-<PERSON><PERSON>-<PERSON><PERSON>,BSD-3-<PERSON><PERSON>,MIT,ISC,Python-2.0,PostgreSQL,X11,Zlib
          --config tools/osv-scanner/license-scan-config.toml
          ./
