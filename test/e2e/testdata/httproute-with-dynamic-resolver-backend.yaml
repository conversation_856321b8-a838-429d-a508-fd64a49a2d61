apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httproute-with-dynamic-resolver-backend
  namespace: gateway-conformance-infra
spec:
  parentRefs:
  - name: same-namespace
  rules:
  - backendRefs:
    - group: gateway.envoyproxy.io
      kind: Backend
      name: backend-dynamic-resolver
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: httproute-with-dynamic-resolver-backend-with-app-protocol
  namespace: gateway-conformance-infra
spec:
  parentRefs:
  - name: same-namespace
  rules:
  - backendRefs:
    - group: gateway.envoyproxy.io
      kind: Backend
      name: backend-dynamic-resolver-with-app-protocol
      match:
        type: PathPrefix
        value: /status
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: backend-dynamic-resolver
  namespace: gateway-conformance-infra
spec:
  type: DynamicResolver
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: backend-dynamic-resolver-with-app-protocol
  namespace: gateway-conformance-infra
spec:
  type: DynamicResolver
  appProtocols:
  - gateway.envoyproxy.io/h2c
---
apiVersion: v1
kind: Service
metadata:
  name: test-service-foo
  namespace: gateway-conformance-infra
spec:
  selector:
    app: infra-backend-v1
  ports:
  - protocol: TCP
    port: 80
    name: http11
    targetPort: 3000
---
apiVersion: v1
kind: Service
metadata:
  name: test-service-bar
  namespace: gateway-conformance-infra
spec:
  selector:
    app: infra-backend-v1
  ports:
  - protocol: TCP
    port: 80
    name: http11
    targetPort: 3000
