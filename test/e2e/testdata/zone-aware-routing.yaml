apiVersion: apps/v1
kind: Deployment
metadata:
  name: zone-aware-backend-local
  namespace: gateway-conformance-infra
  labels:
    app: zone-aware-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zone-aware-backend
  template:
    metadata:
      labels:
        app: zone-aware-backend
    spec:
      containers:
        - name: zone-aware-backend
          # From https://github.com/kubernetes-sigs/gateway-api/blob/main/conformance/echo-basic/echo-basic.go
          image: gcr.io/k8s-staging-gateway-api/echo-basic:v20231214-v1.0.0-140-gf544a46e
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: SERVICE_NAME
              value: zone-aware-backend
          resources:
            requests:
              cpu: 10m
      affinity:
        podAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: "gateway.envoyproxy.io/owning-gateway-name"
                    operator: In
                    values:
                      - same-namespace
                  - key: "app.kubernetes.io/component"
                    operator: In
                    values:
                      - proxy
              topologyKey: topology.kubernetes.io/zone
              namespaceSelector: {}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zone-aware-backend-nonlocal
  namespace: gateway-conformance-infra
  labels:
    app: zone-aware-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zone-aware-backend
  template:
    metadata:
      labels:
        app: zone-aware-backend
    spec:
      containers:
        - name: zone-aware-backend
          # From https://github.com/kubernetes-sigs/gateway-api/blob/main/conformance/echo-basic/echo-basic.go
          image: gcr.io/k8s-staging-gateway-api/echo-basic:v20231214-v1.0.0-140-gf544a46e
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: SERVICE_NAME
              value: zone-aware-backend
          resources:
            requests:
              cpu: 10m
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: "gateway.envoyproxy.io/owning-gateway-name"
                    operator: In
                    values:
                      - same-namespace
                  - key: "app.kubernetes.io/component"
                    operator: In
                    values:
                      - proxy
              topologyKey: topology.kubernetes.io/zone
              namespaceSelector: {}
---
apiVersion: v1
kind: Service
metadata:
  name: zone-aware-backend
  namespace: gateway-conformance-infra
  annotations:
    service.kubernetes.io/topology-mode: Auto
spec:
  selector:
    app: zone-aware-backend
  ports:
    - protocol: TCP
      port: 8080
      name: http11
      targetPort: 3000
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: zone-aware-http-route
  namespace: gateway-conformance-infra
spec:
  parentRefs:
    - name: same-namespace
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - name: zone-aware-backend
          port: 8080
          weight: 1
