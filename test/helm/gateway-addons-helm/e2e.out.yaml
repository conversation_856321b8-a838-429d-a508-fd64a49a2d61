---
# Source: gateway-addons-helm/charts/alloy/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: alloy
  namespace: monitoring
  labels:
    helm.sh/chart: alloy-0.9.2
    app.kubernetes.io/name: alloy
    app.kubernetes.io/instance: gateway-addons-helm
    
    app.kubernetes.io/version: "v1.4.3"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: alloy
    app.kubernetes.io/component: rbac
---
# Source: gateway-addons-helm/charts/loki/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: loki
  labels:
    helm.sh/chart: loki-4.8.0
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "2.7.3"
    app.kubernetes.io/managed-by: Helm
automountServiceAccountToken: true
---
# Source: gateway-addons-helm/charts/opentelemetry-collector/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: otel-collector
  namespace: monitoring
  labels:
    helm.sh/chart: opentelemetry-collector-0.117.3
    app.kubernetes.io/name: opentelemetry-collector
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "0.120.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: standalone-collector
---
# Source: gateway-addons-helm/charts/prometheus/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: v2.52.0
    helm.sh/chart: prometheus-25.21.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: prometheus
  name: prometheus
  namespace: monitoring
  annotations:
    {}
---
# Source: gateway-addons-helm/charts/tempo/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tempo
  namespace: monitoring
  labels:
    helm.sh/chart: tempo-1.3.1
    app.kubernetes.io/name: tempo
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "2.1.1"
    app.kubernetes.io/managed-by: Helm
automountServiceAccountToken: true
---
# Source: gateway-addons-helm/charts/alloy/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: alloy
  labels:
    helm.sh/chart: alloy-0.9.2
    app.kubernetes.io/name: alloy
    app.kubernetes.io/instance: gateway-addons-helm
    
    app.kubernetes.io/version: "v1.4.3"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: alloy
    app.kubernetes.io/component: config
data:
  config.alloy: |-
    // Write your Alloy config here:
    logging {
      level = "info"
      format = "logfmt"
    }
    loki.write "alloy" {
      endpoint {
        url = "http://loki.monitoring.svc:3100/loki/api/v1/push"
      }
    }
    // discovery.kubernetes allows you to find scrape targets from Kubernetes resources.
    // It watches cluster state and ensures targets are continually synced with what is currently running in your cluster.
    discovery.kubernetes "pod" {
      role = "pod"
    }
    
    // discovery.relabel rewrites the label set of the input targets by applying one or more relabeling rules.
    // If no rules are defined, then the input targets are exported as-is.
    discovery.relabel "pod_logs" {
      targets = discovery.kubernetes.pod.targets
    
      // Label creation - "namespace" field from "__meta_kubernetes_namespace"
      rule {
        source_labels = ["__meta_kubernetes_namespace"]
        action = "replace"
        target_label = "namespace"
      }
    
      // Label creation - "pod" field from "__meta_kubernetes_pod_name"
      rule {
        source_labels = ["__meta_kubernetes_pod_name"]
        action = "replace"
        target_label = "pod"
      }
    
      // Label creation - "container" field from "__meta_kubernetes_pod_container_name"
      rule {
        source_labels = ["__meta_kubernetes_pod_container_name"]
        action = "replace"
        target_label = "container"
      }
    
      // Label creation -  "app" field from "__meta_kubernetes_pod_label_app_kubernetes_io_name"
      rule {
        source_labels = ["__meta_kubernetes_pod_label_app_kubernetes_io_name"]
        action = "replace"
        target_label = "app"
      }
    
      // Label creation -  "job" field from "__meta_kubernetes_namespace" and "__meta_kubernetes_pod_container_name"
      // Concatenate values __meta_kubernetes_namespace/__meta_kubernetes_pod_container_name
      rule {
        source_labels = ["__meta_kubernetes_namespace", "__meta_kubernetes_pod_container_name"]
        action = "replace"
        target_label = "job"
        separator = "/"
        replacement = "$1"
      }
    
      // Label creation - "container" field from "__meta_kubernetes_pod_uid" and "__meta_kubernetes_pod_container_name"
      // Concatenate values __meta_kubernetes_pod_uid/__meta_kubernetes_pod_container_name.log
      rule {
        source_labels = ["__meta_kubernetes_pod_uid", "__meta_kubernetes_pod_container_name"]
        action = "replace"
        target_label = "__path__"
        separator = "/"
        replacement = "/var/log/pods/*$1/*.log"
      }
    
      // Label creation -  "container_runtime" field from "__meta_kubernetes_pod_container_id"
      rule {
        source_labels = ["__meta_kubernetes_pod_container_id"]
        action = "replace"
        target_label = "container_runtime"
        regex = "^(\\S+):\\/\\/.+$"
        replacement = "$1"
      }
    }
    
    // loki.source.kubernetes tails logs from Kubernetes containers using the Kubernetes API.
    loki.source.kubernetes "pod_logs" {
      targets    = discovery.relabel.pod_logs.output
      forward_to = [loki.process.pod_logs.receiver]
    }
    // loki.process receives log entries from other Loki components, applies one or more processing stages,
    // and forwards the results to the list of receivers in the component’s arguments.
    loki.process "pod_logs" {
      stage.static_labels {
          values = {
            cluster = "envoy-gateway",
          }
      }
    
      forward_to = [loki.write.alloy.receiver]
    }
---
# Source: gateway-addons-helm/charts/loki/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: loki
  labels:
    helm.sh/chart: loki-4.8.0
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "2.7.3"
    app.kubernetes.io/managed-by: Helm
data:
  config.yaml: |
    auth_enabled: false
    common:
      compactor_address: 'loki'
      path_prefix: /var/loki
      replication_factor: 1
      storage:
        filesystem:
          chunks_directory: /var/loki/chunks
          rules_directory: /var/loki/rules
    limits_config:
      enforce_metric_name: false
      max_cache_freshness_per_query: 10m
      reject_old_samples: true
      reject_old_samples_max_age: 168h
      split_queries_by_interval: 15m
    memberlist:
      join_members:
      - loki-memberlist
    query_range:
      align_queries_with_step: true
    ruler:
      storage:
        type: local
    runtime_config:
      file: /etc/loki/runtime-config/runtime-config.yaml
    schema_config:
      configs:
      - from: "2022-01-11"
        index:
          period: 24h
          prefix: loki_index_
        object_store: filesystem
        schema: v12
        store: boltdb-shipper
    server:
      grpc_listen_port: 9095
      http_listen_port: 3100
    storage_config:
      hedging:
        at: 250ms
        max_per_second: 20
        up_to: 3
    table_manager:
      retention_deletes_enabled: false
      retention_period: 0
---
# Source: gateway-addons-helm/charts/loki/templates/runtime-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: loki-runtime
  labels:
    helm.sh/chart: loki-4.8.0
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "2.7.3"
    app.kubernetes.io/managed-by: Helm
data:
  runtime-config.yaml: |
    
    {}
---
# Source: gateway-addons-helm/charts/opentelemetry-collector/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: otel-collector
  namespace: monitoring
  labels:
    helm.sh/chart: opentelemetry-collector-0.117.3
    app.kubernetes.io/name: opentelemetry-collector
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "0.120.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: standalone-collector
data:
  relay: |
    exporters:
      debug:
        verbosity: detailed
      loki:
        endpoint: http://loki.monitoring.svc:3100/loki/api/v1/push
      otlp:
        endpoint: tempo.monitoring.svc:4317
        tls:
          insecure: true
      prometheus:
        endpoint: '[${env:MY_POD_IP}]:19001'
    extensions:
      health_check:
        endpoint: '[${env:MY_POD_IP}]:13133'
    processors:
      attributes:
        actions:
        - action: insert
          key: loki.attribute.labels
          value: k8s.pod.name, k8s.namespace.name
      batch: {}
      memory_limiter:
        check_interval: 5s
        limit_percentage: 80
        spike_limit_percentage: 25
    receivers:
      datadog:
        endpoint: '[${env:MY_POD_IP}]:8126'
      envoyals:
        endpoint: '[${env:MY_POD_IP}]:9000'
      jaeger:
        protocols:
          grpc:
            endpoint: '[${env:MY_POD_IP}]:14250'
          thrift_compact:
            endpoint: '[${env:MY_POD_IP}]:6831'
          thrift_http:
            endpoint: '[${env:MY_POD_IP}]:14268'
      otlp:
        protocols:
          grpc:
            endpoint: '[${env:MY_POD_IP}]:4317'
          http:
            endpoint: '[${env:MY_POD_IP}]:4318'
      prometheus:
        config:
          scrape_configs:
          - job_name: opentelemetry-collector
            scrape_interval: 10s
            static_configs:
            - targets:
              - '[${env:MY_POD_IP}]:8888'
      zipkin:
        endpoint: '[${env:MY_POD_IP}]:9411'
    service:
      extensions:
      - health_check
      pipelines:
        logs:
          exporters:
          - loki
          processors:
          - attributes
          receivers:
          - otlp
          - envoyals
        metrics:
          exporters:
          - prometheus
          processors:
          - memory_limiter
          - batch
          receivers:
          - datadog
          - otlp
        traces:
          exporters:
          - otlp
          processors:
          - memory_limiter
          - batch
          receivers:
          - datadog
          - otlp
          - zipkin
      telemetry:
        metrics:
          level: none
          readers:
          - pull:
              exporter:
                prometheus:
                  host: localhost
                  port: 8888
---
# Source: gateway-addons-helm/charts/prometheus/templates/cm.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: v2.52.0
    helm.sh/chart: prometheus-25.21.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: prometheus
  name: prometheus
  namespace: monitoring
data:
  allow-snippet-annotations: "false"
  alerting_rules.yml: |
    {}
  alerts: |
    {}
  prometheus.yml: |
    global:
      evaluation_interval: 1m
      scrape_interval: 15s
      scrape_timeout: 10s
    rule_files:
    - /etc/config/recording_rules.yml
    - /etc/config/alerting_rules.yml
    - /etc/config/rules
    - /etc/config/alerts
    scrape_configs:
    - job_name: prometheus
      static_configs:
      - targets:
        - localhost:9090
    - bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      job_name: kubernetes-apiservers
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      - action: keep
        regex: default;kubernetes;https
        source_labels:
        - __meta_kubernetes_namespace
        - __meta_kubernetes_service_name
        - __meta_kubernetes_endpoint_port_name
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        insecure_skip_verify: true
    - bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      job_name: kubernetes-nodes
      kubernetes_sd_configs:
      - role: node
      relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - replacement: kubernetes.default.svc:443
        target_label: __address__
      - regex: (.+)
        replacement: /api/v1/nodes/$1/proxy/metrics
        source_labels:
        - __meta_kubernetes_node_name
        target_label: __metrics_path__
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        insecure_skip_verify: true
    - bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      job_name: kubernetes-nodes-cadvisor
      kubernetes_sd_configs:
      - role: node
      relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - replacement: kubernetes.default.svc:443
        target_label: __address__
      - regex: (.+)
        replacement: /api/v1/nodes/$1/proxy/metrics/cadvisor
        source_labels:
        - __meta_kubernetes_node_name
        target_label: __metrics_path__
      scheme: https
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        insecure_skip_verify: true
    - honor_labels: true
      job_name: kubernetes-service-endpoints
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scrape
      - action: drop
        regex: true
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scrape_slow
      - action: replace
        regex: (https?)
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scheme
        target_label: __scheme__
      - action: replace
        regex: (.+)
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_path
        target_label: __metrics_path__
      - action: replace
        regex: (.+?)(?::\d+)?;(\d+)
        replacement: $1:$2
        source_labels:
        - __address__
        - __meta_kubernetes_service_annotation_prometheus_io_port
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_service_annotation_prometheus_io_param_(.+)
        replacement: __param_$1
      - action: labelmap
        regex: __meta_kubernetes_service_label_(.+)
      - action: replace
        source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - action: replace
        source_labels:
        - __meta_kubernetes_service_name
        target_label: service
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_node_name
        target_label: node
    - honor_labels: true
      job_name: kubernetes-service-endpoints-slow
      kubernetes_sd_configs:
      - role: endpoints
      relabel_configs:
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scrape_slow
      - action: replace
        regex: (https?)
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_scheme
        target_label: __scheme__
      - action: replace
        regex: (.+)
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_path
        target_label: __metrics_path__
      - action: replace
        regex: (.+?)(?::\d+)?;(\d+)
        replacement: $1:$2
        source_labels:
        - __address__
        - __meta_kubernetes_service_annotation_prometheus_io_port
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_service_annotation_prometheus_io_param_(.+)
        replacement: __param_$1
      - action: labelmap
        regex: __meta_kubernetes_service_label_(.+)
      - action: replace
        source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - action: replace
        source_labels:
        - __meta_kubernetes_service_name
        target_label: service
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_node_name
        target_label: node
      scrape_interval: 5m
      scrape_timeout: 30s
    - honor_labels: true
      job_name: prometheus-pushgateway
      kubernetes_sd_configs:
      - role: service
      relabel_configs:
      - action: keep
        regex: pushgateway
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_probe
    - honor_labels: true
      job_name: kubernetes-services
      kubernetes_sd_configs:
      - role: service
      metrics_path: /probe
      params:
        module:
        - http_2xx
      relabel_configs:
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_service_annotation_prometheus_io_probe
      - source_labels:
        - __address__
        target_label: __param_target
      - replacement: blackbox
        target_label: __address__
      - source_labels:
        - __param_target
        target_label: instance
      - action: labelmap
        regex: __meta_kubernetes_service_label_(.+)
      - source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - source_labels:
        - __meta_kubernetes_service_name
        target_label: service
    - honor_labels: true
      job_name: kubernetes-pods
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_scrape
      - action: drop
        regex: true
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_scrape_slow
      - action: replace
        regex: (https?)
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_scheme
        target_label: __scheme__
      - action: replace
        regex: (.+)
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_path
        target_label: __metrics_path__
      - action: replace
        regex: (\d+);(([A-Fa-f0-9]{1,4}::?){1,7}[A-Fa-f0-9]{1,4})
        replacement: '[$2]:$1'
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_port
        - __meta_kubernetes_pod_ip
        target_label: __address__
      - action: replace
        regex: (\d+);((([0-9]+?)(\.|$)){4})
        replacement: $2:$1
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_port
        - __meta_kubernetes_pod_ip
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_annotation_prometheus_io_param_(.+)
        replacement: __param_$1
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - action: replace
        source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_name
        target_label: pod
      - action: drop
        regex: Pending|Succeeded|Failed|Completed
        source_labels:
        - __meta_kubernetes_pod_phase
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_node_name
        target_label: node
    - honor_labels: true
      job_name: kubernetes-pods-slow
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      - action: keep
        regex: true
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_scrape_slow
      - action: replace
        regex: (https?)
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_scheme
        target_label: __scheme__
      - action: replace
        regex: (.+)
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_path
        target_label: __metrics_path__
      - action: replace
        regex: (\d+);(([A-Fa-f0-9]{1,4}::?){1,7}[A-Fa-f0-9]{1,4})
        replacement: '[$2]:$1'
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_port
        - __meta_kubernetes_pod_ip
        target_label: __address__
      - action: replace
        regex: (\d+);((([0-9]+?)(\.|$)){4})
        replacement: $2:$1
        source_labels:
        - __meta_kubernetes_pod_annotation_prometheus_io_port
        - __meta_kubernetes_pod_ip
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_annotation_prometheus_io_param_(.+)
        replacement: __param_$1
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - action: replace
        source_labels:
        - __meta_kubernetes_namespace
        target_label: namespace
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_name
        target_label: pod
      - action: drop
        regex: Pending|Succeeded|Failed|Completed
        source_labels:
        - __meta_kubernetes_pod_phase
      - action: replace
        source_labels:
        - __meta_kubernetes_pod_node_name
        target_label: node
      scrape_interval: 5m
      scrape_timeout: 30s
  recording_rules.yml: |
    {}
  rules: |
    {}
---
# Source: gateway-addons-helm/charts/tempo/templates/configmap-tempo.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: tempo
  namespace: monitoring
  labels:
    helm.sh/chart: tempo-1.3.1
    app.kubernetes.io/name: tempo
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "2.1.1"
    app.kubernetes.io/managed-by: Helm
data:
  overrides.yaml: |
    overrides:
      {}
  tempo.yaml: |
    multitenancy_enabled: false
    usage_report:
      reporting_enabled: true
    compactor:
      compaction:
        block_retention: 24h
    distributor:
      receivers:
            jaeger:
              protocols:
                grpc:
                  endpoint: 0.0.0.0:14250
                thrift_binary:
                  endpoint: 0.0.0.0:6832
                thrift_compact:
                  endpoint: 0.0.0.0:6831
                thrift_http:
                  endpoint: 0.0.0.0:14268
            otlp:
              protocols:
                grpc:
                  endpoint: 0.0.0.0:4317
                http:
                  endpoint: 0.0.0.0:4318
    ingester:
          {}
    server:
          http_listen_port: 3100
    storage:
          trace:
            backend: local
            local:
              path: /var/tempo/traces
            wal:
              path: /var/tempo/wal
    querier:
          {}
    query_frontend:
          {}
    overrides:
          per_tenant_override_config: /conf/overrides.yaml
---
# Source: gateway-addons-helm/templates/dashboards_config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: 'monitoring'
data:
  envoy-clusters.json: |-
      {
        "annotations": {
          "list": [
            {
              "builtIn": 1,
              "datasource": {
                "type": "datasource",
                "uid": "grafana"
              },
              "enable": true,
              "hide": true,
              "iconColor": "rgba(0, 211, 255, 1)",
              "name": "Annotations & Alerts",
              "type": "dashboard"
            }
          ]
        },
        "description": "Envoy proxy monitoring Dashboard with cluster and service level templates. ",
        "editable": true,
        "fiscalYearStartMonth": 0,
        "gnetId": 11021,
        "graphTooltip": 0,
        "id": 2,
        "links": [],
        "liveNow": false,
        "panels": [
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [
                  {
                    "options": {
                      "match": "null",
                      "result": {
                        "text": "N/A"
                      }
                    },
                    "type": "special"
                  }
                ],
                "max": 3,
                "min": 0,
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "#d44a3a",
                      "value": null
                    },
                    {
                      "color": "rgba(237, 129, 40, 0.89)",
                      "value": 1
                    },
                    {
                      "color": "#299c46",
                      "value": 2
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 5,
              "w": 5,
              "x": 0,
              "y": 0
            },
            "id": 9,
            "maxDataPoints": 100,
            "options": {
              "minVizHeight": 75,
              "minVizWidth": 75,
              "orientation": "horizontal",
              "reduceOptions": {
                "calcs": [
                  "mean"
                ],
                "fields": "",
                "values": false
              },
              "showThresholdLabels": false,
              "showThresholdMarkers": true,
              "sizing": "auto"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum(envoy_server_live{})",
                "format": "time_series",
                "interval": "",
                "intervalFactor": 1,
                "legendFormat": "",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Live servers",
            "type": "gauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [
                  {
                    "options": {
                      "match": "null",
                      "result": {
                        "text": "N/A"
                      }
                    },
                    "type": "special"
                  }
                ],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "s"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 5,
              "w": 4,
              "x": 5,
              "y": 0
            },
            "id": 12,
            "maxDataPoints": 100,
            "options": {
              "colorMode": "value",
              "graphMode": "none",
              "justifyMode": "auto",
              "orientation": "horizontal",
              "reduceOptions": {
                "calcs": [
                  "mean"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "auto",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "avg(envoy_server_uptime)",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Avg uptime per node",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [
                  {
                    "options": {
                      "match": "null",
                      "result": {
                        "text": "N/A"
                      }
                    },
                    "type": "special"
                  }
                ],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "decbytes"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 5,
              "w": 5,
              "x": 9,
              "y": 0
            },
            "id": 11,
            "maxDataPoints": 100,
            "options": {
              "colorMode": "none",
              "graphMode": "none",
              "justifyMode": "auto",
              "orientation": "horizontal",
              "reduceOptions": {
                "calcs": [
                  "mean"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "auto",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "builder",
                "expr": "sum(envoy_server_memory_allocated{})",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Allocated Memory",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [
                  {
                    "options": {
                      "match": "null",
                      "result": {
                        "text": "N/A"
                      }
                    },
                    "type": "special"
                  }
                ],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "decbytes"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 5,
              "w": 4,
              "x": 14,
              "y": 0
            },
            "id": 13,
            "maxDataPoints": 100,
            "options": {
              "colorMode": "none",
              "graphMode": "none",
              "justifyMode": "auto",
              "orientation": "horizontal",
              "reduceOptions": {
                "calcs": [
                  "mean"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "auto",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum(envoy_server_memory_heap_size)",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Heap Size",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [
                  {
                    "options": {
                      "match": "null",
                      "result": {
                        "text": "N/A"
                      }
                    },
                    "type": "special"
                  }
                ],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 5,
              "w": 3,
              "x": 18,
              "y": 0
            },
            "id": 19,
            "maxDataPoints": 100,
            "options": {
              "colorMode": "none",
              "graphMode": "none",
              "justifyMode": "auto",
              "orientation": "horizontal",
              "reduceOptions": {
                "calcs": [
                  "mean"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "auto",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "(sum(envoy_cluster_membership_healthy{envoy_cluster_name=~\"$cluster\"})  - sum(envoy_cluster_membership_total{envoy_cluster_name=~\"$cluster\"}))",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Unhealthy Clusters",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [
                  {
                    "options": {
                      "0": {
                        "text": "NOT WELL"
                      },
                      "1": {
                        "text": "OK"
                      }
                    },
                    "type": "value"
                  },
                  {
                    "options": {
                      "match": "null",
                      "result": {
                        "text": "N/A"
                      }
                    },
                    "type": "special"
                  }
                ],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "#d44a3a",
                      "value": null
                    },
                    {
                      "color": "rgba(237, 129, 40, 0.89)",
                      "value": 0
                    },
                    {
                      "color": "#299c46",
                      "value": 1
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 5,
              "w": 3,
              "x": 21,
              "y": 0
            },
            "id": 20,
            "maxDataPoints": 100,
            "options": {
              "colorMode": "value",
              "graphMode": "none",
              "justifyMode": "auto",
              "orientation": "horizontal",
              "reduceOptions": {
                "calcs": [
                  "mean"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "auto",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "(sum(envoy_cluster_membership_total{envoy_cluster_name=~\"$cluster\"})-sum(envoy_cluster_membership_healthy{envoy_cluster_name=~\"$cluster\"})) == bool 0",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Cluster State",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 12,
              "x": 0,
              "y": 5
            },
            "id": 2,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum(envoy_cluster_upstream_cx_active{envoy_cluster_name=~\"$cluster\"}) by (envoy_cluster_name)",
                "format": "time_series",
                "intervalFactor": 2,
                "legendFormat": "{{envoy_cluster_name}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Total active connections",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "${datasource}"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": [
                {
                  "__systemRef": "hideSeriesFrom",
                  "matcher": {
                    "id": "byNames",
                    "options": {
                      "mode": "exclude",
                      "names": [
                        "httproute/default/backend/rule/0"
                      ],
                      "prefix": "All except:",
                      "readOnly": true
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": false,
                        "tooltip": false,
                        "viz": true
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 7,
              "w": 12,
              "x": 12,
              "y": 5
            },
            "id": 4,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "PBFA97CFB590B2093"
                },
                "editorMode": "code",
                "expr": "sum(irate(envoy_cluster_upstream_rq_total{envoy_cluster_name=~\"$cluster\"}[5m])) by (envoy_cluster_name)",
                "format": "time_series",
                "intervalFactor": 2,
                "legendFormat": "{{envoy_cluster_name}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Total requests",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "decbytes"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 12,
              "x": 0,
              "y": 12
            },
            "id": 15,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum(irate(envoy_cluster_upstream_cx_rx_bytes_total{envoy_cluster_name=~\"$cluster\"}[5m])) by (envoy_cluster_name)",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{envoy_cluster_name}} - in",
                "range": true,
                "refId": "A"
              },
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum(irate(envoy_cluster_upstream_cx_tx_bytes_total{envoy_cluster_name=~\"$cluster\"}[5m])) by (envoy_cluster_name)",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{envoy_cluster_name}} - out",
                "range": true,
                "refId": "B"
              }
            ],
            "title": "Upstream Network Traffic",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "decbytes"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 12,
              "x": 12,
              "y": 12
            },
            "id": 17,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "builder",
                "expr": "sum(irate(envoy_http_downstream_cx_rx_bytes_total{envoy_http_conn_manager_prefix=~\"http.*\"}[5m]))",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{service}} - in",
                "range": true,
                "refId": "A"
              },
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "builder",
                "expr": "sum(irate(envoy_http_downstream_cx_tx_bytes_total{envoy_http_conn_manager_prefix=~\"http.*\"}[5m]))",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{service}} - out",
                "range": true,
                "refId": "B"
              }
            ],
            "title": "Downstream Network Traffic",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "${datasource}"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "ms"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 12,
              "x": 0,
              "y": 19
            },
            "id": 22,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "code",
                "expr": "histogram_quantile(0.99, sum(rate(envoy_cluster_upstream_rq_time_bucket{envoy_cluster_name=~\"$cluster\"}[5m])) by (le, envoy_cluster_name))",
                "instant": false,
                "legendFormat": "{{envoy_cluster_name}} 99%",
                "range": true,
                "refId": "A"
              },
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "code",
                "expr": "histogram_quantile(0.9, sum(rate(envoy_cluster_upstream_rq_time_bucket{envoy_cluster_name=~\"$cluster\"}[5m])) by (le, envoy_cluster_name))",
                "hide": false,
                "instant": false,
                "legendFormat": "{{envoy_cluster_name}} 90%",
                "range": true,
                "refId": "B"
              },
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "code",
                "expr": "histogram_quantile(0.5, sum(rate(envoy_cluster_upstream_rq_time_bucket{envoy_cluster_name=~\"$cluster\"}[5m])) by (le, envoy_cluster_name))",
                "hide": false,
                "instant": false,
                "legendFormat": "{{envoy_cluster_name}} 50%",
                "range": true,
                "refId": "C"
              }
            ],
            "title": "Upstream Latency",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "${datasource}"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 12,
              "y": 19
            },
            "id": 24,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "code",
                "expr": "sum(rate(envoy_cluster_upstream_rq_xx{envoy_response_code_class=~\"2\", envoy_cluster_name=~\"$cluster\"}[5m])) by (envoy_cluster_name)",
                "instant": false,
                "legendFormat": "{{envoy_cluster_name}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Upstream 2xx Responses",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "${datasource}"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 18,
              "y": 19
            },
            "id": 28,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "code",
                "expr": "sum(rate(envoy_cluster_upstream_rq_xx{envoy_cluster_name=~\"$cluster\",envoy_response_code_class=~\"4\"}[1m])) by (envoy_cluster_name)",
                "instant": false,
                "legendFormat": "{{envoy_cluster_name}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Upstream 4xx Responses",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 12,
              "x": 0,
              "y": 27
            },
            "id": 7,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum(envoy_cluster_membership_healthy{envoy_cluster_name=~\"$cluster\"})",
                "format": "time_series",
                "intervalFactor": 2,
                "legendFormat": "healthy",
                "range": true,
                "refId": "A"
              },
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "expr": "sum(envoy_cluster_membership_total{envoy_cluster_name=~\"$cluster\",service=~\"$service\"})",
                "format": "time_series",
                "intervalFactor": 2,
                "legendFormat": "total",
                "refId": "B"
              }
            ],
            "title": "Downstream members",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "${datasource}"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 12,
              "y": 27
            },
            "id": 30,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "code",
                "expr": "sum(rate(envoy_cluster_upstream_rq_xx{envoy_cluster_name=~\"$cluster\",envoy_response_code_class=~\"5\"}[5m])) by (envoy_cluster_name)",
                "instant": false,
                "legendFormat": "{{envoy_cluster_name}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Upstream 5xx Responses",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "${datasource}"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 18,
              "y": 27
            },
            "id": 26,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "code",
                "expr": "sum(rate(envoy_cluster_upstream_rq_xx{envoy_cluster_name=~\"$cluster\",envoy_response_code_class=~\"3\"}[5m])) by (envoy_cluster_name)",
                "instant": false,
                "legendFormat": "{{envoy_cluster_name}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Upstream 3xx Responses",
            "type": "timeseries"
          }
        ],
        "refresh": "30s",
        "schemaVersion": 39,
        "tags": [
          "Data Plane"
        ],
        "templating": {
          "list": [
            {
              "current": {
                "selected": false,
                "text": "Prometheus",
                "value": "Prometheus"
              },
              "hide": 0,
              "includeAll": false,
              "multi": false,
              "name": "datasource",
              "options": [],
              "query": "prometheus",
              "refresh": 1,
              "regex": "",
              "skipUrlSync": false,
              "type": "datasource"
            },
            {
              "current": {
                "selected": false,
                "text": "httproute/default/backend/rule/0",
                "value": "httproute/default/backend/rule/0"
              },
              "datasource": {
                "uid": "$datasource"
              },
              "definition": "label_values(envoy_cluster_name)",
              "hide": 0,
              "includeAll": true,
              "label": "Cluster",
              "multi": false,
              "name": "cluster",
              "options": [],
              "query": {
                "query": "label_values(envoy_cluster_name)",
                "refId": "PrometheusVariableQueryEditor-VariableQuery"
              },
              "refresh": 1,
              "regex": "",
              "skipUrlSync": false,
              "sort": 1,
              "tagValuesQuery": "",
              "tagsQuery": "",
              "type": "query",
              "useTags": false
            }
          ]
        },
        "time": {
          "from": "now-15m",
          "to": "now"
        },
        "timeRangeUpdatedDuringEditOrView": false,
        "timepicker": {
          "refresh_intervals": [
            "5s",
            "10s",
            "30s",
            "1m",
            "5m",
            "15m",
            "30m",
            "1h",
            "2h",
            "1d"
          ],
          "time_options": [
            "5m",
            "15m",
            "1h",
            "6h",
            "12h",
            "24h",
            "2d",
            "7d",
            "30d"
          ]
        },
        "timezone": "",
        "title": "Envoy Clusters",
        "uid": "8WkEOMnANKE6PW5hhpVv",
        "version": 1,
        "weekStart": ""
      }
  envoy-gateway-global.json: |-
      {
        "annotations": {
          "list": [
            {
              "builtIn": 1,
              "datasource": {
                "type": "grafana",
                "uid": "-- Grafana --"
              },
              "enable": true,
              "hide": true,
              "iconColor": "rgba(0, 211, 255, 1)",
              "name": "Annotations & Alerts",
              "type": "dashboard"
            }
          ]
        },
        "description": "Envoy Gateway monitoring Dashboard with exported metrics.",
        "editable": true,
        "fiscalYearStartMonth": 0,
        "graphTooltip": 0,
        "links": [],
        "panels": [
          {
            "collapsed": false,
            "gridPos": {
              "h": 1,
              "w": 24,
              "x": 0,
              "y": 0
            },
            "id": 2,
            "panels": [],
            "title": "Watching Components",
            "type": "row"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "How long in seconds a subscribed watchable is handled.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 7,
              "x": 0,
              "y": 1
            },
            "id": 1,
            "maxPerRow": 3,
            "options": {
              "displayMode": "basic",
              "maxVizHeight": 300,
              "minVizHeight": 16,
              "minVizWidth": 8,
              "namePlacement": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showUnfilled": true,
              "sizing": "auto",
              "valueMode": "color"
            },
            "pluginVersion": "11.0.0",
            "repeat": "Runner",
            "repeatDirection": "v",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(le) (watchable_subscribe_duration_seconds_bucket{runner=~\"$Runner\", namespace=\"$Namespace\"})",
                "format": "heatmap",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "{{le}}",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Duration Bucket: $Runner",
            "type": "bargauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "ms"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 3,
              "x": 7,
              "y": 1
            },
            "id": 24,
            "options": {
              "colorMode": "value",
              "graphMode": "none",
              "justifyMode": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "value_and_name",
              "wideLayout": false
            },
            "pluginVersion": "11.0.0",
            "repeat": "Runner",
            "repeatDirection": "v",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "exemplar": false,
                "expr": "avg by(runner) (watchable_subscribe_duration_seconds_sum{runner=~\"$Runner\", namespace=\"$Namespace\"})",
                "format": "time_series",
                "fullMetaSearch": false,
                "hide": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "Avg",
                "range": true,
                "refId": "A",
                "useBackend": false
              },
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "max by(runner) (watchable_subscribe_duration_seconds_sum{runner=~\"$Runner\", namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "hide": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "Max",
                "range": true,
                "refId": "B",
                "useBackend": false
              }
            ],
            "title": "Duration Status",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Current depth of watchable map.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "fixedColor": "super-light-blue",
                  "mode": "shades"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 2,
              "x": 10,
              "y": 1
            },
            "id": 7,
            "options": {
              "colorMode": "value",
              "graphMode": "none",
              "justifyMode": "center",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "value",
              "wideLayout": false
            },
            "pluginVersion": "11.0.0",
            "repeat": "Runner",
            "repeatDirection": "v",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(runner) (watchable_depth{runner=~\"$Runner\", namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Depth",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "fixedColor": "light-blue",
                  "mode": "shades"
                },
                "mappings": [],
                "noValue": "0",
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byName",
                    "options": "Success"
                  },
                  "properties": [
                    {
                      "id": "displayName",
                      "value": "Success"
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 7,
              "w": 7,
              "x": 12,
              "y": 1
            },
            "id": 10,
            "options": {
              "colorMode": "value",
              "graphMode": "none",
              "justifyMode": "center",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "value_and_name",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "repeat": "Runner",
            "repeatDirection": "v",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(status) (watchable_subscribe_total{runner=\"$Runner\", namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": false,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              },
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum(watchable_subscribe_total{runner=\"$Runner\", namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "hide": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "Total",
                "range": true,
                "refId": "B",
                "useBackend": false
              }
            ],
            "title": "Statistics",
            "type": "stat"
          },
          {
            "collapsed": false,
            "gridPos": {
              "h": 1,
              "w": 24,
              "x": 0,
              "y": 36
            },
            "id": 35,
            "panels": [],
            "title": "Status Updater",
            "type": "row"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Total number of panics recovered in the system.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 7,
              "x": 0,
              "y": 8
            },
            "id": 25,
            "options": {
              "colorMode": "value",
              "graphMode": "none",
              "justifyMode": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "value_and_name",
              "wideLayout": false
            },
            "pluginVersion": "11.0.0",
            "repeatDirection": "v",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum(watchable_panics_recovered_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "Recovered Panics",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Recovered Panics",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "How long a status update takes to finish for all Kind.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "fixedColor": "super-light-blue",
                  "mode": "thresholds"
                },
                "mappings": [],
                "min": 0,
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 9,
              "w": 6,
              "x": 0,
              "y": 37
            },
            "id": 61,
            "options": {
              "displayMode": "basic",
              "maxVizHeight": 300,
              "minVizHeight": 16,
              "minVizWidth": 8,
              "namePlacement": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showUnfilled": true,
              "sizing": "auto",
              "valueMode": "color"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(le) (status_update_duration_seconds_bucket{namespace=\"$Namespace\"})",
                "format": "heatmap",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "{{le}}",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Total Duration Bucket",
            "type": "bargauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [],
                "min": 0,
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "#EAB839",
                      "value": 0.2
                    },
                    {
                      "color": "red",
                      "value": 0.5
                    }
                  ]
                },
                "unit": "s"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 9,
              "w": 6,
              "x": 6,
              "y": 37
            },
            "id": 82,
            "options": {
              "displayMode": "gradient",
              "maxVizHeight": 300,
              "minVizHeight": 16,
              "minVizWidth": 8,
              "namePlacement": "auto",
              "orientation": "vertical",
              "reduceOptions": {
                "calcs": [
                  "mean"
                ],
                "fields": "",
                "values": false
              },
              "showUnfilled": true,
              "sizing": "auto",
              "valueMode": "color"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "status_update_duration_seconds_sum{namespace=\"$Namespace\"}",
                "format": "time_series",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "{{kind}}",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Avg Duration",
            "type": "bargauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "continuous-GrYlRd"
                },
                "mappings": [],
                "min": 0,
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "#EAB839",
                      "value": 0.1
                    },
                    {
                      "color": "red",
                      "value": 0.5
                    }
                  ]
                },
                "unit": "s"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 9,
              "w": 6,
              "x": 12,
              "y": 37
            },
            "id": 83,
            "options": {
              "displayMode": "gradient",
              "maxVizHeight": 300,
              "minVizHeight": 16,
              "minVizWidth": 8,
              "namePlacement": "auto",
              "orientation": "vertical",
              "reduceOptions": {
                "calcs": [
                  "max"
                ],
                "fields": "",
                "values": false
              },
              "showUnfilled": true,
              "sizing": "auto",
              "valueMode": "color"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "status_update_duration_seconds_sum{namespace=\"$Namespace\"}",
                "format": "time_series",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "{{kind}}",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Max Duration",
            "type": "bargauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "continuous-BlPu"
                },
                "mappings": [],
                "min": 0,
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "yellow",
                      "value": 0.01
                    },
                    {
                      "color": "red",
                      "value": 0.1
                    }
                  ]
                },
                "unit": "s"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 9,
              "w": 6,
              "x": 18,
              "y": 37
            },
            "id": 84,
            "options": {
              "displayMode": "gradient",
              "maxVizHeight": 300,
              "minVizHeight": 16,
              "minVizWidth": 8,
              "namePlacement": "auto",
              "orientation": "vertical",
              "reduceOptions": {
                "calcs": [
                  "logmin"
                ],
                "fields": "",
                "values": false
              },
              "showUnfilled": true,
              "sizing": "auto",
              "valueMode": "color"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "status_update_duration_seconds_sum{namespace=\"$Namespace\"}",
                "format": "time_series",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "{{kind}}",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Min Duration",
            "type": "bargauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Total number of status updates by object kind.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": true,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "left",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineStyle": {
                    "fill": "solid"
                  },
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "fieldMinMax": false,
                "mappings": [],
                "min": 0,
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 10,
              "x": 0,
              "y": 46
            },
            "id": 56,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "pluginVersion": "10.4.1",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(kind) (status_update_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "{{kind}}",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Total",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": true,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "left",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "min": 0,
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 10,
              "x": 10,
              "y": 46
            },
            "id": 57,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(status) (status_update_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Status",
            "type": "timeseries"
          },
          {
            "collapsed": false,
            "gridPos": {
              "h": 1,
              "w": 24,
              "x": 0,
              "y": 54
            },
            "id": 126,
            "panels": [],
            "title": "xDS Server",
            "type": "row"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "fixedColor": "super-light-green",
                  "mode": "shades"
                },
                "mappings": [],
                "min": 0,
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 10,
              "x": 0,
              "y": 55
            },
            "id": 127,
            "options": {
              "colorMode": "value",
              "graphMode": "area",
              "justifyMode": "center",
              "orientation": "vertical",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "value_and_name",
              "wideLayout": false
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum(xds_snapshot_create_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "Total",
                "range": true,
                "refId": "A",
                "useBackend": false
              },
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(status) (xds_snapshot_create_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "hide": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "{{status}}",
                "range": true,
                "refId": "B",
                "useBackend": false
              }
            ],
            "title": "Snapshot Creation Status",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "fixedColor": "orange",
                  "mode": "shades"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 5,
              "x": 10,
              "y": 55
            },
            "id": 149,
            "options": {
              "colorMode": "value",
              "graphMode": "none",
              "justifyMode": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "auto",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "max(xds_stream_duration_seconds_bucket{namespace=\"$Namespace\", isDeltaStream=\"true\"})",
                "format": "heatmap",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Finished Stream",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Maximum duration seconds for finished xDS delta stream connection.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "fixedColor": "red",
                  "mode": "shades"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    }
                  ]
                },
                "unit": "s"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 4,
              "w": 4,
              "x": 15,
              "y": 55
            },
            "id": 150,
            "options": {
              "colorMode": "value",
              "graphMode": "none",
              "justifyMode": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "max"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "auto",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "xds_stream_duration_seconds_sum{namespace=\"$Namespace\", isDeltaStream=\"true\"}",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Max Duration",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Minimum duration seconds for finished xDS delta stream connection.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "fixedColor": "light-green",
                  "mode": "shades"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    }
                  ]
                },
                "unit": "s"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 4,
              "w": 4,
              "x": 15,
              "y": 59
            },
            "id": 151,
            "options": {
              "colorMode": "value",
              "graphMode": "none",
              "justifyMode": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "min"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "auto",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "xds_stream_duration_seconds_sum{namespace=\"$Namespace\", isDeltaStream=\"true\"}",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Min Duration",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Total number of xds snapshot cache updates by node id.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic-by-name"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 20,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 3,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 10,
              "x": 0,
              "y": 63
            },
            "id": 152,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": false
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "pluginVersion": "10.4.1",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(nodeID) (xds_snapshot_update_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Update Total",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 5,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 9,
              "x": 10,
              "y": 63
            },
            "id": 153,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": false
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "pluginVersion": "10.4.1",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(status) (xds_snapshot_update_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "{{nodeID}}",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Update Status",
            "type": "timeseries"
          },
          {
            "collapsed": false,
            "gridPos": {
              "h": 1,
              "w": 24,
              "x": 0,
              "y": 71
            },
            "id": 156,
            "panels": [],
            "title": "Infrastructure Manager",
            "type": "row"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 0,
              "y": 72
            },
            "id": 199,
            "options": {
              "displayMode": "gradient",
              "maxVizHeight": 300,
              "minVizHeight": 16,
              "minVizWidth": 8,
              "namePlacement": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showUnfilled": true,
              "sizing": "auto",
              "valueMode": "color"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(le) (resource_apply_duration_seconds_bucket{namespace=\"$Namespace\"})",
                "format": "heatmap",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "{{le}}",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Total Apply Duration Bucket",
            "type": "bargauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "orange",
                      "value": 0.3
                    },
                    {
                      "color": "red",
                      "value": 0.5
                    }
                  ]
                },
                "unit": "s"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 6,
              "y": 72
            },
            "id": 220,
            "options": {
              "displayMode": "gradient",
              "maxVizHeight": 300,
              "minVizHeight": 16,
              "minVizWidth": 8,
              "namePlacement": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "mean"
                ],
                "fields": "",
                "values": false
              },
              "showUnfilled": true,
              "sizing": "auto",
              "valueMode": "color"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(kind) (resource_apply_duration_seconds_sum{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Avg Apply Duration",
            "type": "bargauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "continuous-GrYlRd"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "orange",
                      "value": 0.3
                    },
                    {
                      "color": "red",
                      "value": 0.5
                    }
                  ]
                },
                "unit": "s"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 12,
              "y": 72
            },
            "id": 221,
            "options": {
              "displayMode": "gradient",
              "maxVizHeight": 300,
              "minVizHeight": 16,
              "minVizWidth": 8,
              "namePlacement": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "max"
                ],
                "fields": "",
                "values": false
              },
              "showUnfilled": true,
              "sizing": "auto",
              "valueMode": "color"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(kind) (resource_apply_duration_seconds_sum{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Max Apply Duration",
            "type": "bargauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "continuous-BlPu"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "orange",
                      "value": 0.3
                    },
                    {
                      "color": "red",
                      "value": 0.5
                    }
                  ]
                },
                "unit": "s"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 18,
              "y": 72
            },
            "id": 222,
            "options": {
              "displayMode": "gradient",
              "maxVizHeight": 300,
              "minVizHeight": 16,
              "minVizWidth": 8,
              "namePlacement": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "logmin"
                ],
                "fields": "",
                "values": false
              },
              "showUnfilled": true,
              "sizing": "auto",
              "valueMode": "color"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(kind) (resource_apply_duration_seconds_sum{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Min Apply Duration",
            "type": "bargauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Total number of applied resources sumed by kind.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 25,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "normal"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 8,
              "x": 0,
              "y": 80
            },
            "id": 157,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "hidden",
                "placement": "right",
                "showLegend": false
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(kind) (resource_apply_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Total Applied Resources",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Total number of applied resources that succeed sumed by kind.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 25,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "normal"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 8,
              "x": 8,
              "y": 80
            },
            "id": 229,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "hidden",
                "placement": "right",
                "showLegend": false
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(status) (resource_apply_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Applied Resources Status",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Total number of applied resources sumed by infra name.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 8,
              "x": 16,
              "y": 80
            },
            "id": 178,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "pluginVersion": "10.4.1",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(name) (resource_apply_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Total Applied Infrastructures",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 0,
              "y": 87
            },
            "id": 223,
            "options": {
              "displayMode": "gradient",
              "maxVizHeight": 300,
              "minVizHeight": 16,
              "minVizWidth": 8,
              "namePlacement": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showUnfilled": true,
              "sizing": "auto",
              "valueMode": "color"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "code",
                "expr": "sum by(le) (resource_delete_duration_seconds_bucket{namespace=\"$Namespace\"})",
                "format": "heatmap",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "{{le}}",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Total Delete Duration Bucket",
            "type": "bargauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "orange",
                      "value": 0.1
                    },
                    {
                      "color": "red",
                      "value": 0.3
                    }
                  ]
                },
                "unit": "s"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 6,
              "y": 87
            },
            "id": 224,
            "options": {
              "displayMode": "gradient",
              "maxVizHeight": 300,
              "minVizHeight": 16,
              "minVizWidth": 8,
              "namePlacement": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "mean"
                ],
                "fields": "",
                "values": false
              },
              "showUnfilled": true,
              "sizing": "auto",
              "valueMode": "color"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "code",
                "expr": "sum by(kind) (resource_delete_duration_seconds_sum{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Avg Delete Duration",
            "type": "bargauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "continuous-GrYlRd"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "orange",
                      "value": 0.1
                    },
                    {
                      "color": "red",
                      "value": 0.3
                    }
                  ]
                },
                "unit": "s"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 12,
              "y": 87
            },
            "id": 225,
            "options": {
              "displayMode": "gradient",
              "maxVizHeight": 300,
              "minVizHeight": 16,
              "minVizWidth": 8,
              "namePlacement": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "max"
                ],
                "fields": "",
                "values": false
              },
              "showUnfilled": true,
              "sizing": "auto",
              "valueMode": "color"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "code",
                "expr": "sum by(kind) (resource_delete_duration_seconds_sum{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Max Delete Duration",
            "type": "bargauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "continuous-BlPu"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "orange",
                      "value": 0.1
                    },
                    {
                      "color": "red",
                      "value": 0.3
                    }
                  ]
                },
                "unit": "s"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 18,
              "y": 87
            },
            "id": 226,
            "options": {
              "displayMode": "gradient",
              "maxVizHeight": 300,
              "minVizHeight": 16,
              "minVizWidth": 8,
              "namePlacement": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "logmin"
                ],
                "fields": "",
                "values": false
              },
              "showUnfilled": true,
              "sizing": "auto",
              "valueMode": "color"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "code",
                "expr": "sum by(kind) (resource_delete_duration_seconds_sum{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Min Delete Duration",
            "type": "bargauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Total number of deleted resources sumed by kind.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 25,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "normal"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 8,
              "x": 0,
              "y": 95
            },
            "id": 227,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "hidden",
                "placement": "right",
                "showLegend": false
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(kind) (resource_delete_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Total Deleted Resources",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Total number of deleted resources that succeed sumed by kind.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 25,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 8,
              "x": 8,
              "y": 95
            },
            "id": 232,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "hidden",
                "placement": "right",
                "showLegend": false
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(status) (resource_delete_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Deleted Resources Status",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Total number of deleted resources sumed by infra name.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 8,
              "x": 16,
              "y": 95
            },
            "id": 228,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "pluginVersion": "10.4.1",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(name) (resource_delete_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Total Deleted Infrastructures",
            "type": "timeseries"
          },
          {
            "collapsed": false,
            "gridPos": {
              "h": 1,
              "w": 24,
              "x": 0,
              "y": 102
            },
            "id": 249,
            "panels": [],
            "title": "Wasm",
            "type": "row"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 6,
              "w": 4,
              "x": 0,
              "y": 103
            },
            "id": 250,
            "options": {
              "minVizHeight": 75,
              "minVizWidth": 75,
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showThresholdLabels": false,
              "showThresholdMarkers": true,
              "sizing": "auto"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "PBFA97CFB590B2093"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "wasm_cache_entries{namespace=\"$Namespace\"}",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Cache Entries",
            "type": "gauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Total number of Wasm remote fetch cache lookups.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 6,
              "w": 7,
              "x": 4,
              "y": 103
            },
            "id": 251,
            "options": {
              "colorMode": "value",
              "graphMode": "area",
              "justifyMode": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "auto",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "PBFA97CFB590B2093"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(hit) (wasm_cache_lookup_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "hit={{hit}}",
                "range": true,
                "refId": "A",
                "useBackend": false
              }
            ],
            "title": "Cache Lookups",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Total number of Wasm remote fetches and results.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 6,
              "w": 7,
              "x": 11,
              "y": 103
            },
            "id": 252,
            "options": {
              "colorMode": "value",
              "graphMode": "area",
              "justifyMode": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "auto",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "PBFA97CFB590B2093"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum by(status) (wasm_remote_fetch_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "{{status}}",
                "range": true,
                "refId": "A",
                "useBackend": false
              },
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "disableTextWrap": false,
                "editorMode": "builder",
                "expr": "sum(wasm_remote_fetch_total{namespace=\"$Namespace\"})",
                "fullMetaSearch": false,
                "hide": false,
                "includeNullMetadata": true,
                "instant": false,
                "legendFormat": "Total",
                "range": true,
                "refId": "B",
                "useBackend": false
              }
            ],
            "title": "Cache Remote Fetches",
            "type": "stat"
          }
        ],
        "refresh": "",
        "schemaVersion": 39,
        "tags": [
          "Control Plane"
        ],
        "templating": {
          "list": [
            {
              "current": {
                "selected": false,
                "text": "Prometheus",
                "value": "PBFA97CFB590B2093"
              },
              "hide": 0,
              "includeAll": false,
              "multi": false,
              "name": "datasource",
              "options": [],
              "query": "prometheus",
              "refresh": 1,
              "regex": "",
              "skipUrlSync": false,
              "type": "datasource"
            },
            {
              "allValue": ".*",
              "current": {
                "selected": false,
                "text": "envoy-gateway-system",
                "value": "envoy-gateway-system"
              },
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "definition": "label_values(watchable_depth,namespace)",
              "hide": 0,
              "includeAll": false,
              "multi": false,
              "name": "Namespace",
              "options": [],
              "query": {
                "qryType": 1,
                "query": "label_values(watchable_depth,namespace)",
                "refId": "PrometheusVariableQueryEditor-VariableQuery"
              },
              "refresh": 1,
              "regex": "",
              "skipUrlSync": false,
              "sort": 0,
              "type": "query"
            },
            {
              "allValue": ".*",
              "current": {
                "selected": true,
                "text": [
                  "All"
                ],
                "value": [
                  "$__all"
                ]
              },
              "datasource": {
                "type": "prometheus",
                "uid": "$datasource"
              },
              "definition": "label_values(watchable_depth,runner)",
              "hide": 0,
              "includeAll": true,
              "multi": true,
              "name": "Runner",
              "options": [],
              "query": {
                "qryType": 1,
                "query": "label_values(watchable_depth,runner)",
                "refId": "PrometheusVariableQueryEditor-VariableQuery"
              },
              "refresh": 2,
              "regex": "",
              "skipUrlSync": false,
              "sort": 0,
              "type": "query"
            }
          ]
        },
        "time": {
          "from": "now-6h",
          "to": "now"
        },
        "timeRangeUpdatedDuringEditOrView": false,
        "timepicker": {},
        "timezone": "browser",
        "title": "Envoy Gateway Global",
        "uid": "bdn8lriao7myoa",
        "version": 1,
        "weekStart": ""
      }
  envoy-proxy-global.json: |-
      {
        "annotations": {
          "list": [
            {
              "builtIn": 1,
              "datasource": {
                "type": "datasource",
                "uid": "grafana"
              },
              "enable": true,
              "hide": true,
              "iconColor": "rgba(0, 211, 255, 1)",
              "name": "Annotations & Alerts",
              "type": "dashboard"
            }
          ]
        },
        "description": "Envoy proxy monitoring Dashboard with service level templates.",
        "editable": true,
        "fiscalYearStartMonth": 0,
        "gnetId": 11022,
        "graphTooltip": 0,
        "id": 3,
        "links": [],
        "liveNow": false,
        "panels": [
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [
                  {
                    "options": {
                      "match": "null",
                      "result": {
                        "text": "N/A"
                      }
                    },
                    "type": "special"
                  }
                ],
                "max": 3,
                "min": 0,
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "#d44a3a",
                      "value": null
                    },
                    {
                      "color": "rgba(237, 129, 40, 0.89)",
                      "value": 1
                    },
                    {
                      "color": "#299c46",
                      "value": 2
                    }
                  ]
                },
                "unit": "none"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 5,
              "x": 0,
              "y": 0
            },
            "id": 37,
            "maxDataPoints": 100,
            "options": {
              "minVizHeight": 75,
              "minVizWidth": 75,
              "orientation": "horizontal",
              "reduceOptions": {
                "calcs": [
                  "mean"
                ],
                "fields": "",
                "values": false
              },
              "showThresholdLabels": false,
              "showThresholdMarkers": true,
              "sizing": "auto"
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "builder",
                "expr": "sum(envoy_server_live)",
                "format": "time_series",
                "interval": "",
                "intervalFactor": 1,
                "legendFormat": "",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Live servers",
            "type": "gauge"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [
                  {
                    "options": {
                      "match": "null",
                      "result": {
                        "text": "N/A"
                      }
                    },
                    "type": "special"
                  }
                ],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "s"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 5,
              "x": 5,
              "y": 0
            },
            "id": 39,
            "maxDataPoints": 100,
            "options": {
              "colorMode": "value",
              "graphMode": "none",
              "justifyMode": "auto",
              "orientation": "auto",
              "reduceOptions": {
                "calcs": [
                  "lastNotNull"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "auto",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "builder",
                "expr": "avg by(pod) (envoy_server_uptime{namespace=~\"$Namespace\"})",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Avg uptime per node",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [
                  {
                    "options": {
                      "match": "null",
                      "result": {
                        "text": "N/A"
                      }
                    },
                    "type": "special"
                  }
                ],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "decbytes"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 5,
              "x": 10,
              "y": 0
            },
            "id": 43,
            "maxDataPoints": 100,
            "options": {
              "colorMode": "none",
              "graphMode": "none",
              "justifyMode": "auto",
              "orientation": "horizontal",
              "reduceOptions": {
                "calcs": [
                  "mean"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "auto",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum by(pod) (envoy_server_memory_heap_size{namespace=~\"$Namespace\"})",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Heap Size",
            "type": "stat"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "mappings": [
                  {
                    "options": {
                      "match": "null",
                      "result": {
                        "text": "N/A"
                      }
                    },
                    "type": "special"
                  }
                ],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "decbytes"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 7,
              "w": 5,
              "x": 15,
              "y": 0
            },
            "id": 41,
            "maxDataPoints": 100,
            "options": {
              "colorMode": "none",
              "graphMode": "none",
              "justifyMode": "auto",
              "orientation": "horizontal",
              "reduceOptions": {
                "calcs": [
                  "mean"
                ],
                "fields": "",
                "values": false
              },
              "showPercentChange": false,
              "textMode": "auto",
              "wideLayout": true
            },
            "pluginVersion": "11.0.0",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "$datasource"
                },
                "editorMode": "builder",
                "expr": "sum by(pod) (envoy_server_memory_allocated{namespace=~\"$Namespace\"})",
                "hide": false,
                "instant": false,
                "range": true,
                "refId": "B"
              }
            ],
            "title": "Allocated Memory",
            "type": "stat"
          },
          {
            "collapsed": false,
            "datasource": {
              "type": "datasource",
              "uid": "grafana"
            },
            "gridPos": {
              "h": 1,
              "w": 24,
              "x": 0,
              "y": 7
            },
            "id": 24,
            "panels": [],
            "targets": [
              {
                "datasource": {
                  "type": "datasource",
                  "uid": "grafana"
                },
                "refId": "A"
              }
            ],
            "title": "DownStream",
            "type": "row"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 0,
              "y": 8
            },
            "id": 3,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum(rate(envoy_http_downstream_rq_total[5m]))",
                "format": "time_series",
                "intervalFactor": 2,
                "legendFormat": "Envoy HTTP Downstream Rq total",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Downstream RPS",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "${datasource}"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 6,
              "y": 8
            },
            "id": 9,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "builder",
                "expr": "sum by(namespace) (rate(envoy_http_downstream_cx_total{namespace=~\"$Namespace\"}[5m]))",
                "instant": false,
                "legendFormat": "{{namespace}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Downstream CPS",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "ms"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 12,
              "y": 8
            },
            "id": 16,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "histogram_quantile(0.9, sum by(le) (rate(envoy_http_downstream_rq_time_bucket[5m])))",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{service}} 90%",
                "range": true,
                "refId": "A"
              },
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "builder",
                "expr": "histogram_quantile(0.5, sum by(le) (rate(envoy_http_downstream_rq_time_bucket[5m])))",
                "format": "time_series",
                "hide": false,
                "intervalFactor": 1,
                "legendFormat": "{{service}} 50% ",
                "range": true,
                "refId": "B"
              },
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "builder",
                "expr": "histogram_quantile(0.99, sum by(le) (rate(envoy_http_downstream_rq_time_bucket[5m])))",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{service}} 99%",
                "range": true,
                "refId": "C"
              }
            ],
            "title": "Downstream Latency",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 18,
              "y": 8
            },
            "id": 4,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum by(namespace) (envoy_listener_downstream_cx_active{namespace=~\"$Namespace\"})",
                "format": "time_series",
                "instant": false,
                "interval": "",
                "intervalFactor": 2,
                "legendFormat": "{{namespace}}",
                "refId": "A"
              }
            ],
            "title": "Downstream Total Connections",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "${datasource}"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 0,
              "y": 16
            },
            "id": 32,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "builder",
                "expr": "sum by(namespace) (rate(envoy_tcp_downstream_cx_total{namespace=~\"$Namespace\"}[5m]))",
                "instant": false,
                "legendFormat": "{{namespace}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "TCP Downstream CPS",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "${datasource}"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 6,
              "y": 16
            },
            "id": 31,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "builder",
                "expr": "sum by(namespace) (rate(envoy_tcp_downstream_cx_rx_bytes_total{namespace=~\"$Namespace\"}[5m]))",
                "instant": false,
                "legendFormat": "{{namespace}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "TCP Downstream Bytes Rx/second",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "${datasource}"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisBorderShow": false,
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "insertNulls": false,
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green",
                      "value": null
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 12,
              "y": 16
            },
            "id": 33,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "maxHeight": 600,
                "mode": "single",
                "sort": "none"
              }
            },
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "builder",
                "expr": "sum by(namespace) (rate(envoy_tcp_downstream_cx_tx_bytes_total{namespace=~\"$Namespace\"}[5m]))",
                "instant": false,
                "legendFormat": "{{namespace}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "TCP Downstream Bytes Tx/Second",
            "type": "timeseries"
          },
          {
            "collapsed": false,
            "datasource": {
              "type": "datasource",
              "uid": "grafana"
            },
            "gridPos": {
              "h": 1,
              "w": 24,
              "x": 0,
              "y": 24
            },
            "id": 22,
            "panels": [],
            "targets": [
              {
                "datasource": {
                  "type": "datasource",
                  "uid": "grafana"
                },
                "refId": "A"
              }
            ],
            "title": "UpStream",
            "type": "row"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "Displays the number of Requests per Second being performed against each Upstream.",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 0,
              "y": 25
            },
            "id": 2,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "builder",
                "expr": "sum by(namespace) (rate(envoy_cluster_upstream_rq_total{namespace=~\"$Namespace\"}[5m]))",
                "format": "time_series",
                "intervalFactor": 2,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Upstream RPS",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 6,
              "y": 25
            },
            "id": 14,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum(rate(envoy_cluster_upstream_cx_total{namespace=~\"$Namespace\",}[5m])) by (namespace)",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "__auto",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Upstream CPS",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "ms"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 12,
              "y": 25
            },
            "id": 10,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "histogram_quantile(0.99, sum(rate(envoy_cluster_upstream_rq_time_bucket{namespace=~\"$Namespace\"}[5m])) by (le, namespace))",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{namespace}} 99%",
                "range": true,
                "refId": "A"
              },
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "histogram_quantile(0.9, sum(rate(envoy_cluster_upstream_rq_time_bucket{namespace=~\"$Namespace\"}[5m])) by (le, namespace))",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{namespace}} 90%",
                "range": true,
                "refId": "C"
              },
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "histogram_quantile(0.5, sum(rate(envoy_cluster_upstream_rq_time_bucket{namespace=~\"$Namespace\"}[5m])) by (le, namespace))",
                "format": "time_series",
                "hide": false,
                "intervalFactor": 1,
                "legendFormat": "{{namespace}} 50% ",
                "range": true,
                "refId": "B"
              }
            ],
            "title": "Upstream Latency",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 18,
              "y": 25
            },
            "id": 15,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum(envoy_cluster_upstream_cx_active{namespace=~\"$Namespace\"}) by (namespace)",
                "format": "time_series",
                "intervalFactor": 1,
                "legendFormat": "{{namespace}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Upstream Total Connections",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "${datasource}"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 0,
              "y": 33
            },
            "id": 34,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "single",
                "sort": "none"
              }
            },
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "builder",
                "expr": "sum by(namespace) (rate(envoy_cluster_upstream_cx_rx_bytes_total{namespace=~\"$Namespace\"}[5m]))",
                "instant": false,
                "legendFormat": "{{namespace}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Upstream Bytes Rx/Second",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "${datasource}"
            },
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 0,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "auto",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                }
              },
              "overrides": []
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 6,
              "y": 33
            },
            "id": 35,
            "options": {
              "legend": {
                "calcs": [],
                "displayMode": "list",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "single",
                "sort": "none"
              }
            },
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "builder",
                "expr": "sum by(namespace) (rate(envoy_cluster_upstream_cx_tx_bytes_total{namespace=~\"$Namespace\"}[5m]))",
                "instant": false,
                "legendFormat": "{{namespace}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Upstream Bytes Tx/Second",
            "type": "timeseries"
          },
          {
            "collapsed": false,
            "datasource": {
              "type": "datasource",
              "uid": "grafana"
            },
            "gridPos": {
              "h": 1,
              "w": 24,
              "x": 0,
              "y": 41
            },
            "id": 28,
            "panels": [],
            "targets": [
              {
                "datasource": {
                  "type": "datasource",
                  "uid": "grafana"
                },
                "refId": "A"
              }
            ],
            "title": "Upstream Response Codes",
            "type": "row"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "${datasource}"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 0,
              "y": 42
            },
            "id": 5,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "type": "prometheus",
                  "uid": "${datasource}"
                },
                "editorMode": "builder",
                "exemplar": false,
                "expr": "sum(rate(envoy_cluster_upstream_rq_xx{envoy_response_code_class=~\"2\"}[5m]))",
                "format": "time_series",
                "instant": false,
                "interval": "",
                "intervalFactor": 2,
                "legendFormat": "Value",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Upstream 2xx Responses",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 6,
              "y": 42
            },
            "id": 11,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "builder",
                "expr": "sum(rate(envoy_cluster_upstream_rq_xx{envoy_response_code_class=~\"3\"}[5m]))",
                "format": "time_series",
                "intervalFactor": 2,
                "legendFormat": "value",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Upstream 3xx Responses",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 12,
              "y": 42
            },
            "id": 12,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum(rate(envoy_cluster_upstream_rq_xx{envoy_response_code_class=\"4\"}[5m]))",
                "format": "time_series",
                "intervalFactor": 2,
                "legendFormat": "Value",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Upstream 4xx Responses",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 18,
              "y": 42
            },
            "id": 13,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum(rate(envoy_cluster_upstream_rq_xx{envoy_response_code_class=\"5\"}[5m]))",
                "format": "time_series",
                "intervalFactor": 2,
                "legendFormat": "Value",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Upstream 5xx Responses",
            "type": "timeseries"
          },
          {
            "collapsed": false,
            "datasource": {
              "type": "datasource",
              "uid": "grafana"
            },
            "gridPos": {
              "h": 1,
              "w": 24,
              "x": 0,
              "y": 50
            },
            "id": 26,
            "panels": [],
            "targets": [
              {
                "datasource": {
                  "type": "datasource",
                  "uid": "grafana"
                },
                "refId": "A"
              }
            ],
            "title": "Total",
            "type": "row"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "min": 0,
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "percentunit"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 0,
              "y": 51
            },
            "id": 17,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "avg(envoy_cluster_membership_healthy{namespace=~\"$Namespace\"}) by (namespace) / avg(envoy_cluster_membership_total{namespace=~\"$Namespace\"}) by (namespace)",
                "format": "time_series",
                "intervalFactor": 2,
                "legendFormat": "{{namespace}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Endpoint Percentage Health",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "min": 0,
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 6,
              "y": 51
            },
            "id": 19,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "builder",
                "expr": "sum by(namespace) (envoy_cluster_membership_total{namespace=~\"$Namespace\"})",
                "format": "time_series",
                "intervalFactor": 2,
                "legendFormat": "{{namespace}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Total Endpoints",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "min": 0,
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 12,
              "y": 51
            },
            "id": 18,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "builder",
                "expr": "sum by(namespace) (envoy_cluster_membership_healthy{namespace=~\"$Namespace\"})",
                "format": "time_series",
                "intervalFactor": 2,
                "legendFormat": "{{namespace}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Healthy Endpoints",
            "type": "timeseries"
          },
          {
            "datasource": {
              "type": "prometheus",
              "uid": "$datasource"
            },
            "description": "",
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "palette-classic"
                },
                "custom": {
                  "axisCenteredZero": false,
                  "axisColorMode": "text",
                  "axisLabel": "",
                  "axisPlacement": "auto",
                  "barAlignment": 0,
                  "drawStyle": "line",
                  "fillOpacity": 10,
                  "gradientMode": "none",
                  "hideFrom": {
                    "legend": false,
                    "tooltip": false,
                    "viz": false
                  },
                  "lineInterpolation": "linear",
                  "lineWidth": 1,
                  "pointSize": 5,
                  "scaleDistribution": {
                    "type": "linear"
                  },
                  "showPoints": "never",
                  "spanNulls": false,
                  "stacking": {
                    "group": "A",
                    "mode": "none"
                  },
                  "thresholdsStyle": {
                    "mode": "off"
                  }
                },
                "links": [],
                "mappings": [],
                "min": 0,
                "thresholds": {
                  "mode": "absolute",
                  "steps": [
                    {
                      "color": "green"
                    },
                    {
                      "color": "red",
                      "value": 80
                    }
                  ]
                },
                "unit": "short"
              },
              "overrides": [
                {
                  "matcher": {
                    "id": "byValue",
                    "options": {
                      "op": "gte",
                      "reducer": "allIsZero",
                      "value": 0
                    }
                  },
                  "properties": [
                    {
                      "id": "custom.hideFrom",
                      "value": {
                        "legend": true,
                        "tooltip": true,
                        "viz": false
                      }
                    }
                  ]
                }
              ]
            },
            "gridPos": {
              "h": 8,
              "w": 6,
              "x": 18,
              "y": 51
            },
            "id": 20,
            "options": {
              "legend": {
                "calcs": [
                  "mean",
                  "lastNotNull",
                  "max"
                ],
                "displayMode": "table",
                "placement": "bottom",
                "showLegend": true
              },
              "tooltip": {
                "mode": "multi",
                "sort": "none"
              }
            },
            "pluginVersion": "10.0.2",
            "targets": [
              {
                "datasource": {
                  "uid": "$datasource"
                },
                "editorMode": "code",
                "expr": "sum by(namespace) (envoy_cluster_membership_total{namespace=~\"$Namespace\"}) - sum by(namespace) (envoy_cluster_membership_healthy{namespace=~\"$Namespace\"})",
                "format": "time_series",
                "intervalFactor": 2,
                "legendFormat": "{{namespace}}",
                "range": true,
                "refId": "A"
              }
            ],
            "title": "Unhealthy Endpoints",
            "type": "timeseries"
          }
        ],
        "refresh": "10s",
        "schemaVersion": 39,
        "tags": [
          "Data Plane"
        ],
        "templating": {
          "list": [
            {
              "current": {
                "selected": false,
                "text": "Prometheus",
                "value": "PBFA97CFB590B2093"
              },
              "hide": 0,
              "includeAll": false,
              "multi": false,
              "name": "datasource",
              "options": [],
              "query": "prometheus",
              "refresh": 1,
              "regex": "",
              "skipUrlSync": false,
              "type": "datasource"
            },
            {
              "allValue": ".*",
              "current": {
                "selected": true,
                "text": [
                  "All"
                ],
                "value": [
                  "$__all"
                ]
              },
              "datasource": {
                "uid": "$datasource"
              },
              "definition": "",
              "hide": 0,
              "includeAll": true,
              "multi": true,
              "name": "Namespace",
              "options": [],
              "query": "label_values(envoy_cluster_upstream_rq_time_bucket,namespace)",
              "refresh": 2,
              "regex": "",
              "skipUrlSync": false,
              "sort": 0,
              "tagValuesQuery": "",
              "tagsQuery": "",
              "type": "query",
              "useTags": false
            }
          ]
        },
        "time": {
          "from": "now-5m",
          "to": "now"
        },
        "timeRangeUpdatedDuringEditOrView": false,
        "timepicker": {
          "refresh_intervals": [
            "5s",
            "10s",
            "30s",
            "1m",
            "5m",
            "15m",
            "30m",
            "1h",
            "2h",
            "1d"
          ],
          "time_options": [
            "5m",
            "15m",
            "1h",
            "6h",
            "12h",
            "24h",
            "2d",
            "7d",
            "30d"
          ]
        },
        "timezone": "",
        "title": "Envoy Global",
        "uid": "heHhNSFf6Na8vIZWRs8H",
        "version": 1,
        "weekStart": ""
      }
  global-ratelimit.json: |-
      {
          "annotations": {
              "list": [
                  {
                      "builtIn": 1,
                      "datasource": {
                          "type": "grafana",
                          "uid": "-- Grafana --"
                      },
                      "enable": true,
                      "hide": true,
                      "iconColor": "rgba(0, 211, 255, 1)",
                      "name": "Annotations & Alerts",
                      "type": "dashboard"
                  }
              ]
          },
          "description": "Envoy Gateway monitoring Dashboard with exported metrics.",
          "editable": true,
          "fiscalYearStartMonth": 0,
          "graphTooltip": 0,
          "id": 6,
          "links": [],
          "panels": [
              {
                  "collapsed": false,
                  "gridPos": {
                      "h": 1,
                      "w": 24,
                      "x": 0,
                      "y": 0
                  },
                  "id": 273,
                  "panels": [],
                  "title": "Global Ratelimit",
                  "type": "row"
              },
              {
                  "datasource": {
                      "type": "prometheus",
                      "uid": "$datasource"
                  },
                  "description": "The fraction of this program's available CPU time used by the GC since the program started.",
                  "fieldConfig": {
                      "defaults": {
                          "color": {
                              "mode": "palette-classic"
                          },
                          "custom": {
                              "axisBorderShow": false,
                              "axisCenteredZero": false,
                              "axisColorMode": "series",
                              "axisGridShow": false,
                              "axisLabel": "",
                              "axisPlacement": "auto",
                              "barAlignment": 0,
                              "drawStyle": "line",
                              "fillOpacity": 25,
                              "gradientMode": "none",
                              "hideFrom": {
                                  "legend": false,
                                  "tooltip": false,
                                  "viz": false
                              },
                              "insertNulls": false,
                              "lineInterpolation": "linear",
                              "lineWidth": 1,
                              "pointSize": 5,
                              "scaleDistribution": {
                                  "type": "linear"
                              },
                              "showPoints": "auto",
                              "spanNulls": false,
                              "stacking": {
                                  "group": "A",
                                  "mode": "none"
                              },
                              "thresholdsStyle": {
                                  "mode": "off"
                              }
                          },
                          "mappings": [],
                          "thresholds": {
                              "mode": "absolute",
                              "steps": [
                                  {
                                      "color": "green",
                                      "value": null
                                  },
                                  {
                                      "color": "red",
                                      "value": 80
                                  }
                              ]
                          },
                          "unit": "miu"
                      },
                      "overrides": []
                  },
                  "gridPos": {
                      "h": 7,
                      "w": 8,
                      "x": 0,
                      "y": 1
                  },
                  "id": 274,
                  "options": {
                      "legend": {
                          "calcs": [],
                          "displayMode": "hidden",
                          "placement": "right",
                          "showLegend": false
                      },
                      "tooltip": {
                          "maxHeight": 600,
                          "mode": "single",
                          "sort": "none"
                      }
                  },
                  "pluginVersion": "11.0.0",
                  "targets": [
                      {
                          "datasource": {
                              "type": "prometheus",
                              "uid": "$datasource"
                          },
                          "disableTextWrap": false,
                          "editorMode": "code",
                          "exemplar": false,
                          "expr": "(go_memstats_gc_cpu_fraction)*1000000",
                          "format": "time_series",
                          "fullMetaSearch": false,
                          "includeNullMetadata": true,
                          "instant": false,
                          "legendFormat": "__auto",
                          "range": true,
                          "refId": "A",
                          "useBackend": false
                      }
                  ],
                  "title": "CPU Fraction",
                  "transparent": true,
                  "type": "timeseries"
              },
              {
                  "datasource": {
                      "type": "prometheus",
                      "uid": "$datasource"
                  },
                  "description": "Resident memory size",
                  "fieldConfig": {
                      "defaults": {
                          "color": {
                              "mode": "thresholds"
                          },
                          "mappings": [],
                          "thresholds": {
                              "mode": "absolute",
                              "steps": [
                                  {
                                      "color": "green",
                                      "value": null
                                  }
                              ]
                          },
                          "unit": "decmbytes"
                      },
                      "overrides": []
                  },
                  "gridPos": {
                      "h": 7,
                      "w": 7,
                      "x": 9,
                      "y": 1
                  },
                  "id": 291,
                  "options": {
                      "minVizHeight": 75,
                      "minVizWidth": 75,
                      "orientation": "auto",
                      "reduceOptions": {
                          "calcs": [
                              "lastNotNull"
                          ],
                          "fields": "",
                          "values": false
                      },
                      "showThresholdLabels": false,
                      "showThresholdMarkers": false,
                      "sizing": "auto"
                  },
                  "pluginVersion": "11.0.0",
                  "targets": [
                      {
                          "datasource": {
                              "type": "prometheus",
                              "uid": "$datasource"
                          },
                          "disableTextWrap": false,
                          "editorMode": "code",
                          "exemplar": false,
                          "expr": "(process_resident_memory_bytes{app_kubernetes_io_component=\"ratelimit\"})/1000000",
                          "fullMetaSearch": false,
                          "includeNullMetadata": true,
                          "instant": false,
                          "legendFormat": "__auto",
                          "range": true,
                          "refId": "A",
                          "useBackend": false
                      }
                  ],
                  "title": "Resident Memory",
                  "transparent": true,
                  "type": "gauge"
              },
              {
                  "datasource": {
                      "type": "prometheus",
                      "uid": "$datasource"
                  },
                  "description": "Virtual memory size",
                  "fieldConfig": {
                      "defaults": {
                          "color": {
                              "mode": "thresholds"
                          },
                          "mappings": [],
                          "min": 0,
                          "thresholds": {
                              "mode": "percentage",
                              "steps": [
                                  {
                                      "color": "green",
                                      "value": null
                                  }
                              ]
                          },
                          "unit": "decmbytes"
                      },
                      "overrides": []
                  },
                  "gridPos": {
                      "h": 7,
                      "w": 7,
                      "x": 16,
                      "y": 1
                  },
                  "id": 325,
                  "options": {
                      "minVizHeight": 75,
                      "minVizWidth": 75,
                      "orientation": "auto",
                      "reduceOptions": {
                          "calcs": [
                              "lastNotNull"
                          ],
                          "fields": "",
                          "values": false
                      },
                      "showThresholdLabels": false,
                      "showThresholdMarkers": false,
                      "sizing": "auto"
                  },
                  "pluginVersion": "11.0.0",
                  "targets": [
                      {
                          "datasource": {
                              "type": "prometheus",
                              "uid": "$datasource"
                          },
                          "disableTextWrap": false,
                          "editorMode": "code",
                          "exemplar": false,
                          "expr": "(process_virtual_memory_bytes{app_kubernetes_io_component=\"ratelimit\"})/1000000",
                          "fullMetaSearch": false,
                          "includeNullMetadata": true,
                          "instant": false,
                          "legendFormat": "__auto",
                          "range": true,
                          "refId": "A",
                          "useBackend": false
                      }
                  ],
                  "title": "Virtual Memory",
                  "transparent": true,
                  "type": "gauge"
              },
              {
                  "datasource": {
                      "type": "prometheus",
                      "uid": "$datasource"
                  },
                  "description": "Number of ratelimit rule hits in total",
                  "fieldConfig": {
                      "defaults": {
                          "color": {
                              "fixedColor": "light-blue",
                              "mode": "shades"
                          },
                          "fieldMinMax": false,
                          "mappings": [],
                          "noValue": "0",
                          "thresholds": {
                              "mode": "absolute",
                              "steps": [
                                  {
                                      "color": "green",
                                      "value": null
                                  }
                              ]
                          },
                          "unit": "none"
                      },
                      "overrides": []
                  },
                  "gridPos": {
                      "h": 7,
                      "w": 6,
                      "x": 0,
                      "y": 8
                  },
                  "id": 308,
                  "options": {
                      "colorMode": "value",
                      "graphMode": "none",
                      "justifyMode": "center",
                      "orientation": "auto",
                      "reduceOptions": {
                          "calcs": [
                              "lastNotNull"
                          ],
                          "fields": "",
                          "values": false
                      },
                      "showPercentChange": false,
                      "textMode": "value",
                      "wideLayout": false
                  },
                  "pluginVersion": "11.0.0",
                  "targets": [
                      {
                          "datasource": {
                              "type": "prometheus",
                              "uid": "$datasource"
                          },
                          "disableTextWrap": false,
                          "editorMode": "builder",
                          "exemplar": false,
                          "expr": "ratelimit_service_rate_limit_total_hits{domain=\"$DefaultDomain\", key1=\"httproute/default/http-ratelimit/rule/0/match/0/ratelimit_example_httproute/default/http-ratelimit/rule/0/match/0/ratelimit_example\", key2=\"rule-0-match-0_rule-0-match-0\"}",
                          "fullMetaSearch": false,
                          "includeNullMetadata": true,
                          "instant": false,
                          "interval": "",
                          "legendFormat": "__auto",
                          "range": true,
                          "refId": "A",
                          "useBackend": false
                      }
                  ],
                  "title": "Total Hits",
                  "type": "stat"
              },
              {
                  "datasource": {
                      "type": "prometheus",
                      "uid": "$datasource"
                  },
                  "description": "Number of rule hits over the NearLimit ratio threshold (currently 80%) but under the threshold rate.",
                  "fieldConfig": {
                      "defaults": {
                          "color": {
                              "fixedColor": "light-blue",
                              "mode": "shades"
                          },
                          "fieldMinMax": false,
                          "mappings": [],
                          "noValue": "0",
                          "thresholds": {
                              "mode": "absolute",
                              "steps": [
                                  {
                                      "color": "green",
                                      "value": null
                                  }
                              ]
                          },
                          "unit": "none"
                      },
                      "overrides": []
                  },
                  "gridPos": {
                      "h": 7,
                      "w": 6,
                      "x": 6,
                      "y": 8
                  },
                  "id": 326,
                  "options": {
                      "colorMode": "value",
                      "graphMode": "none",
                      "justifyMode": "center",
                      "orientation": "auto",
                      "reduceOptions": {
                          "calcs": [
                              "lastNotNull"
                          ],
                          "fields": "",
                          "values": false
                      },
                      "showPercentChange": false,
                      "textMode": "value",
                      "wideLayout": false
                  },
                  "pluginVersion": "11.0.0",
                  "targets": [
                      {
                          "datasource": {
                              "type": "prometheus",
                              "uid": "$datasource"
                          },
                          "disableTextWrap": false,
                          "editorMode": "builder",
                          "exemplar": false,
                          "expr": "ratelimit_service_rate_limit_near_limit{domain=\"$DefaultDomain\", key1=\"httproute/default/http-ratelimit/rule/0/match/0/ratelimit_example_httproute/default/http-ratelimit/rule/0/match/0/ratelimit_example\", key2=\"rule-0-match-0_rule-0-match-0\"}",
                          "fullMetaSearch": false,
                          "includeNullMetadata": true,
                          "instant": false,
                          "interval": "",
                          "legendFormat": "__auto",
                          "range": true,
                          "refId": "A",
                          "useBackend": false
                      }
                  ],
                  "title": "Near Limit",
                  "type": "stat"
              },
              {
                  "datasource": {
                      "type": "prometheus",
                      "uid": "$datasource"
                  },
                  "description": "Number of rule hits exceeding the threshold rate",
                  "fieldConfig": {
                      "defaults": {
                          "color": {
                              "fixedColor": "light-blue",
                              "mode": "shades"
                          },
                          "fieldMinMax": false,
                          "mappings": [],
                          "noValue": "0",
                          "thresholds": {
                              "mode": "absolute",
                              "steps": [
                                  {
                                      "color": "green",
                                      "value": null
                                  }
                              ]
                          },
                          "unit": "none"
                      },
                      "overrides": []
                  },
                  "gridPos": {
                      "h": 7,
                      "w": 6,
                      "x": 12,
                      "y": 8
                  },
                  "id": 327,
                  "options": {
                      "colorMode": "value",
                      "graphMode": "none",
                      "justifyMode": "center",
                      "orientation": "auto",
                      "reduceOptions": {
                          "calcs": [
                              "lastNotNull"
                          ],
                          "fields": "",
                          "values": false
                      },
                      "showPercentChange": false,
                      "textMode": "value",
                      "wideLayout": false
                  },
                  "pluginVersion": "11.0.0",
                  "targets": [
                      {
                          "datasource": {
                              "type": "prometheus",
                              "uid": "$datasource"
                          },
                          "disableTextWrap": false,
                          "editorMode": "builder",
                          "exemplar": false,
                          "expr": "ratelimit_service_rate_limit_over_limit{domain=\"$DefaultDomain\", key1=\"httproute/default/http-ratelimit/rule/0/match/0/ratelimit_example_httproute/default/http-ratelimit/rule/0/match/0/ratelimit_example\", key2=\"rule-0-match-0_rule-0-match-0\"}",
                          "fullMetaSearch": false,
                          "includeNullMetadata": true,
                          "instant": false,
                          "interval": "",
                          "legendFormat": "__auto",
                          "range": true,
                          "refId": "A",
                          "useBackend": false
                      }
                  ],
                  "title": "Over Limit",
                  "type": "stat"
              }
          ],
          "refresh": "",
          "schemaVersion": 39,
          "tags": [
              "Control Plane"
          ],
          "templating": {
              "list": [
                  {
                      "current": {
                          "selected": false,
                          "text": "Prometheus",
                          "value": "$datasource"
                      },
                      "hide": 0,
                      "includeAll": false,
                      "multi": false,
                      "name": "datasource",
                      "options": [],
                      "query": "prometheus",
                      "refresh": 1,
                      "regex": "",
                      "skipUrlSync": false,
                      "type": "datasource"
                  },
                  {
                      "allValue": ".*",
                      "current": {
                          "selected": false,
                          "text": "envoy-gateway-system",
                          "value": "envoy-gateway-system"
                      },
                      "datasource": {
                          "type": "prometheus",
                          "uid": "$datasource"
                      },
                      "definition": "label_values(watchable_depth,namespace)",
                      "hide": 0,
                      "includeAll": false,
                      "multi": false,
                      "name": "Namespace",
                      "options": [],
                      "query": {
                          "qryType": 1,
                          "query": "label_values(watchable_depth,namespace)",
                          "refId": "PrometheusVariableQueryEditor-VariableQuery"
                      },
                      "refresh": 1,
                      "regex": "",
                      "skipUrlSync": false,
                      "sort": 0,
                      "type": "query"
                  },
                  {
                      "allValue": ".*",
                      "current": {
                          "selected": true,
                          "text": [
                              "All"
                          ],
                          "value": [
                              "$__all"
                          ]
                      },
                      "datasource": {
                          "type": "prometheus",
                          "uid": "$datasource"
                      },
                      "definition": "label_values(watchable_depth,runner)",
                      "hide": 0,
                      "includeAll": true,
                      "multi": true,
                      "name": "Runner",
                      "options": [],
                      "query": {
                          "qryType": 1,
                          "query": "label_values(watchable_depth,runner)",
                          "refId": "PrometheusVariableQueryEditor-VariableQuery"
                      },
                      "refresh": 2,
                      "regex": "",
                      "skipUrlSync": false,
                      "sort": 0,
                      "type": "query"
                  },
                  {
                      "current": {
                          "selected": false,
                          "text": "default/eg/http",
                          "value": "default/eg/http"
                      },
                      "description": "DefaultDomain is set to default/eg/http",
                      "hide": 0,
                      "includeAll": false,
                      "multi": false,
                      "name": "DefaultDomain",
                      "options": [
                          {
                              "selected": true,
                              "text": "default/eg/http",
                              "value": "default/eg/http"
                          }
                      ],
                      "query": "default/eg/http",
                      "skipUrlSync": false,
                      "type": "custom"
                  }
              ]
          },
          "time": {
              "from": "now-6h",
              "to": "now"
          },
          "timeRangeUpdatedDuringEditOrView": false,
          "timepicker": {},
          "timezone": "browser",
          "title": "Global Ratelimit",
          "uid": "R2xvYmFsIFJhdGVsaW1pdAo",
          "version": 4,
          "weekStart": ""
      }
  resources-monitor.gen.json: |-
      {
         "description": "Memory and CPU Usage Monitor for Envoy Gateway and Envoy Proxy.\n",
         "graphTooltip": 1,
         "panels": [
            {
               "collapsed": false,
               "gridPos": {
                  "h": 1,
                  "w": 24,
                  "x": 0,
                  "y": 0
               },
               "id": 1,
               "panels": [ ],
               "title": "Envoy Gateway",
               "type": "row"
            },
            {
               "datasource": {
                  "type": "datasource",
                  "uid": "-- Mixed --"
               },
               "fieldConfig": {
                  "defaults": {
                     "custom": {
                        "fillOpacity": 10,
                        "scaleDistribution": {
                           "log": 10,
                           "type": "log"
                        },
                        "showPoints": "never"
                     },
                     "unit": "s"
                  }
               },
               "gridPos": {
                  "h": 8,
                  "w": 8,
                  "x": 0,
                  "y": 1
               },
               "id": 2,
               "interval": "1m",
               "options": {
                  "legend": {
                     "calcs": [
                        "lastNotNull",
                        "max"
                     ],
                     "displayMode": "table"
                  }
               },
               "pluginVersion": "v11.0.0",
               "targets": [
                  {
                     "datasource": {
                        "type": "prometheus",
                        "uid": "$datasource"
                     },
                     "expr": "sum by (namespace) (\n    rate(\n        container_cpu_usage_seconds_total{\n            container=\"envoy-gateway\"\n        }\n    [$__rate_interval])\n)\n",
                     "intervalFactor": 2,
                     "legendFormat": "{{namespace}}\n"
                  }
               ],
               "title": "CPU Usage",
               "type": "timeseries"
            },
            {
               "datasource": {
                  "type": "datasource",
                  "uid": "-- Mixed --"
               },
               "fieldConfig": {
                  "defaults": {
                     "custom": {
                        "fillOpacity": 10,
                        "scaleDistribution": {
                           "log": 2,
                           "type": "log"
                        },
                        "showPoints": "never"
                     },
                     "unit": "bytes"
                  }
               },
               "gridPos": {
                  "h": 8,
                  "w": 8,
                  "x": 8,
                  "y": 1
               },
               "id": 3,
               "interval": "1m",
               "options": {
                  "legend": {
                     "calcs": [
                        "lastNotNull",
                        "max"
                     ],
                     "displayMode": "table"
                  }
               },
               "pluginVersion": "v11.0.0",
               "targets": [
                  {
                     "datasource": {
                        "type": "prometheus",
                        "uid": "$datasource"
                     },
                     "expr": "sum by (namespace) (\n  container_memory_working_set_bytes{container=\"envoy-gateway\"}\n)\n",
                     "intervalFactor": 2,
                     "legendFormat": "{{namespace}}\n"
                  }
               ],
               "title": "Memory Usage",
               "type": "timeseries"
            },
            {
               "collapsed": false,
               "gridPos": {
                  "h": 1,
                  "w": 24,
                  "x": 0,
                  "y": 9
               },
               "id": 4,
               "panels": [ ],
               "title": "Envoy Proxy",
               "type": "row"
            },
            {
               "datasource": {
                  "type": "datasource",
                  "uid": "-- Mixed --"
               },
               "fieldConfig": {
                  "defaults": {
                     "custom": {
                        "fillOpacity": 10,
                        "scaleDistribution": {
                           "log": 10,
                           "type": "log"
                        },
                        "showPoints": "never"
                     },
                     "unit": "s"
                  }
               },
               "gridPos": {
                  "h": 8,
                  "w": 8,
                  "x": 0,
                  "y": 10
               },
               "id": 5,
               "interval": "1m",
               "options": {
                  "legend": {
                     "calcs": [
                        "lastNotNull",
                        "max"
                     ],
                     "displayMode": "table"
                  }
               },
               "pluginVersion": "v11.0.0",
               "targets": [
                  {
                     "datasource": {
                        "type": "prometheus",
                        "uid": "$datasource"
                     },
                     "expr": "sum by (pod) (\n    rate(\n        container_cpu_usage_seconds_total{\n            container=\"envoy\"\n        }\n    [$__rate_interval])\n)\n",
                     "intervalFactor": 2,
                     "legendFormat": "{{pod}}\n"
                  }
               ],
               "title": "CPU Usage",
               "type": "timeseries"
            },
            {
               "datasource": {
                  "type": "datasource",
                  "uid": "-- Mixed --"
               },
               "fieldConfig": {
                  "defaults": {
                     "custom": {
                        "fillOpacity": 10,
                        "scaleDistribution": {
                           "log": 2,
                           "type": "log"
                        },
                        "showPoints": "never"
                     },
                     "unit": "bytes"
                  }
               },
               "gridPos": {
                  "h": 8,
                  "w": 8,
                  "x": 8,
                  "y": 10
               },
               "id": 6,
               "interval": "1m",
               "options": {
                  "legend": {
                     "calcs": [
                        "lastNotNull",
                        "max"
                     ],
                     "displayMode": "table"
                  }
               },
               "pluginVersion": "v11.0.0",
               "targets": [
                  {
                     "datasource": {
                        "type": "prometheus",
                        "uid": "$datasource"
                     },
                     "expr": "sum by (pod) (\n  container_memory_working_set_bytes{container=\"envoy\"}\n)\n",
                     "intervalFactor": 2,
                     "legendFormat": "{{pod}}\n"
                  }
               ],
               "title": "Memory Usage",
               "type": "timeseries"
            }
         ],
         "schemaVersion": 39,
         "templating": {
            "list": [
               {
                  "name": "datasource",
                  "query": "prometheus",
                  "type": "datasource"
               }
            ]
         },
         "time": {
            "from": "now-6h",
            "to": "now"
         },
         "timezone": "utc",
         "title": "Resources Monitor",
         "uid": "f7aeb41676b7865cf31ae49691325f91"
      }
---
# Source: gateway-addons-helm/charts/alloy/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: alloy
  labels:
    helm.sh/chart: alloy-0.9.2
    app.kubernetes.io/name: alloy
    app.kubernetes.io/instance: gateway-addons-helm
    
    app.kubernetes.io/version: "v1.4.3"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: alloy
    app.kubernetes.io/component: rbac
rules:
  # Rules which allow discovery.kubernetes to function.
  - apiGroups:
      - ""
      - "discovery.k8s.io"
      - "networking.k8s.io"
    resources:
      - endpoints
      - endpointslices
      - ingresses
      - nodes
      - nodes/proxy
      - nodes/metrics
      - pods
      - services
    verbs:
      - get
      - list
      - watch
  # Rules which allow loki.source.kubernetes and loki.source.podlogs to work.
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/log
      - namespaces
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - "monitoring.grafana.com"
    resources:
      - podlogs
    verbs:
      - get
      - list
      - watch
  # Rules which allow mimir.rules.kubernetes to work.
  - apiGroups: ["monitoring.coreos.com"]
    resources:
      - prometheusrules
    verbs:
      - get
      - list
      - watch
  - nonResourceURLs:
      - /metrics
    verbs:
      - get
  # Rules for prometheus.kubernetes.*
  - apiGroups: ["monitoring.coreos.com"]
    resources:
      - podmonitors
      - servicemonitors
      - probes
    verbs:
      - get
      - list
      - watch
  # Rules which allow eventhandler to work.
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - get
      - list
      - watch
  # needed for remote.kubernetes.*
  - apiGroups: [""]
    resources:
      - "configmaps"
      - "secrets"
    verbs:
      - get
      - list
      - watch
  # needed for otelcol.processor.k8sattributes
  - apiGroups: ["apps"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["extensions"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
---
# Source: gateway-addons-helm/charts/prometheus/templates/clusterrole.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: v2.52.0
    helm.sh/chart: prometheus-25.21.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: prometheus
  name: prometheus
rules:
  - apiGroups:
      - ""
    resources:
      - nodes
      - nodes/proxy
      - nodes/metrics
      - services
      - endpoints
      - pods
      - ingresses
      - configmaps
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - "extensions"
      - "networking.k8s.io"
    resources:
      - ingresses/status
      - ingresses
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - "discovery.k8s.io"
    resources:
      - endpointslices
    verbs:
      - get
      - list
      - watch
  - nonResourceURLs:
      - "/metrics"
    verbs:
      - get
---
# Source: gateway-addons-helm/charts/alloy/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: alloy
  labels:
    helm.sh/chart: alloy-0.9.2
    app.kubernetes.io/name: alloy
    app.kubernetes.io/instance: gateway-addons-helm
    
    app.kubernetes.io/version: "v1.4.3"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: alloy
    app.kubernetes.io/component: rbac
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: alloy
subjects:
  - kind: ServiceAccount
    name: alloy
    namespace: monitoring
---
# Source: gateway-addons-helm/charts/prometheus/templates/clusterrolebinding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: v2.52.0
    helm.sh/chart: prometheus-25.21.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: prometheus
  name: prometheus
subjects:
  - kind: ServiceAccount
    name: prometheus
    namespace: monitoring
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus
---
# Source: gateway-addons-helm/charts/alloy/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: alloy
  labels:
    helm.sh/chart: alloy-0.9.2
    app.kubernetes.io/name: alloy
    app.kubernetes.io/instance: gateway-addons-helm
    
    app.kubernetes.io/version: "v1.4.3"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: alloy
    app.kubernetes.io/component: networking
spec:
  type: ClusterIP
  selector:
    app.kubernetes.io/name: alloy
    app.kubernetes.io/instance: gateway-addons-helm
  internalTrafficPolicy: Cluster
  ports:
    - name: http-metrics
      port: 12345
      targetPort: 12345
      protocol: "TCP"
---
# Source: gateway-addons-helm/charts/loki/templates/service-memberlist.yaml
apiVersion: v1
kind: Service
metadata:
  name: loki-memberlist
  labels:
    helm.sh/chart: loki-4.8.0
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "2.7.3"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - name: tcp
      port: 7946
      targetPort: http-memberlist
      protocol: TCP
  selector:
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/part-of: memberlist
---
# Source: gateway-addons-helm/charts/loki/templates/single-binary/service-headless.yaml
apiVersion: v1
kind: Service
metadata:
  name: loki-headless
  namespace: monitoring
  labels:
    helm.sh/chart: loki-4.8.0
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "2.7.3"
    app.kubernetes.io/managed-by: Helm
    variant: headless
    prometheus.io/service-monitor: "false"
spec:
  clusterIP: None
  ports:
    - name: http-metrics
      port: 3100
      targetPort: http-metrics
      protocol: TCP
  selector:
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: gateway-addons-helm
---
# Source: gateway-addons-helm/charts/loki/templates/single-binary/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: loki
  labels:
    helm.sh/chart: loki-4.8.0
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "2.7.3"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  ports:
    - name: http-metrics
      port: 3100
      targetPort: http-metrics
      protocol: TCP
    - name: grpc
      port: 9095
      targetPort: grpc
      protocol: TCP
  selector:
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/component: single-binary
---
# Source: gateway-addons-helm/charts/opentelemetry-collector/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: otel-collector
  namespace: monitoring
  labels:
    helm.sh/chart: opentelemetry-collector-0.117.3
    app.kubernetes.io/name: opentelemetry-collector
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "0.120.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: standalone-collector
    component: standalone-collector
spec:
  type: ClusterIP
  ports:
    
    - name: envoy-als
      port: 9000
      targetPort: 9000
      protocol: TCP
      appProtocol: grpc
    - name: jaeger-compact
      port: 6831
      targetPort: 6831
      protocol: UDP
    - name: jaeger-grpc
      port: 14250
      targetPort: 14250
      protocol: TCP
    - name: jaeger-thrift
      port: 14268
      targetPort: 14268
      protocol: TCP
    - name: otlp
      port: 4317
      targetPort: 4317
      protocol: TCP
      appProtocol: grpc
    - name: otlp-http
      port: 4318
      targetPort: 4318
      protocol: TCP
    - name: zipkin
      port: 9411
      targetPort: 9411
      protocol: TCP
  selector:
    app.kubernetes.io/name: opentelemetry-collector
    app.kubernetes.io/instance: gateway-addons-helm
    component: standalone-collector
  internalTrafficPolicy: Cluster
---
# Source: gateway-addons-helm/charts/prometheus/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: v2.52.0
    helm.sh/chart: prometheus-25.21.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: prometheus
  name: prometheus
  namespace: monitoring
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 9090
  selector:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: gateway-addons-helm
  sessionAffinity: None
  type: "LoadBalancer"
---
# Source: gateway-addons-helm/charts/tempo/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: tempo
  namespace: monitoring
  labels:
    helm.sh/chart: tempo-1.3.1
    app.kubernetes.io/name: tempo
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "2.1.1"
    app.kubernetes.io/managed-by: Helm
spec:
  type: LoadBalancer
  ports:
  - name: tempo-prom-metrics
    port: 3100
    targetPort: 3100
  - name: tempo-jaeger-thrift-compact
    port: 6831
    protocol: UDP
    targetPort: 6831
  - name: tempo-jaeger-thrift-binary
    port: 6832
    protocol: UDP
    targetPort: 6832
  - name: tempo-jaeger-thrift-http
    port: 14268
    protocol: TCP
    targetPort: 14268
  - name: grpc-tempo-jaeger
    port: 14250
    protocol: TCP
    targetPort: 14250
  - name: tempo-zipkin
    port: 9411
    protocol: TCP
    targetPort: 9411
  - name: tempo-otlp-legacy
    port: 55680
    protocol: TCP
    targetPort: 55680
  - name: tempo-otlp-http-legacy
    port: 55681
    protocol: TCP
    targetPort: 4318
  - name: grpc-tempo-otlp
    port: 4317
    protocol: TCP
    targetPort: 4317
  - name: tempo-otlp-http
    port: 4318
    protocol: TCP
    targetPort: 4318
  - name: tempo-opencensus
    port: 55678
    protocol: TCP
    targetPort: 55678
  selector:
    app.kubernetes.io/name: tempo
    app.kubernetes.io/instance: gateway-addons-helm
---
# Source: gateway-addons-helm/charts/alloy/templates/controllers/daemonset.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: alloy
  labels:
    helm.sh/chart: alloy-0.9.2
    app.kubernetes.io/name: alloy
    app.kubernetes.io/instance: gateway-addons-helm
    
    app.kubernetes.io/version: "v1.4.3"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: alloy
spec:
  minReadySeconds: 10
  selector:
    matchLabels:
      app.kubernetes.io/name: alloy
      app.kubernetes.io/instance: gateway-addons-helm
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: alloy
      labels:
        app.kubernetes.io/name: alloy
        app.kubernetes.io/instance: gateway-addons-helm
    spec:
      serviceAccountName: alloy
      containers:
        - name: alloy
          image: docker.io/grafana/alloy:v1.4.3
          imagePullPolicy: IfNotPresent
          args:
            - run
            - /etc/alloy/config.alloy
            - --storage.path=/tmp/alloy
            - --server.http.listen-addr=0.0.0.0:12345
            - --server.http.ui-path-prefix=/
            - --stability.level=generally-available
          env:
            - name: ALLOY_DEPLOY_MODE
              value: "helm"
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          ports:
            - containerPort: 12345
              name: http-metrics
          readinessProbe:
            httpGet:
              path: /-/ready
              port: 12345
              scheme: HTTP
            initialDelaySeconds: 10
            timeoutSeconds: 1
          volumeMounts:
            - name: config
              mountPath: /etc/alloy
        - name: config-reloader
          image: ghcr.io/jimmidyson/configmap-reload:v0.12.0
          args:
            - --volume-dir=/etc/alloy
            - --webhook-url=http://localhost:12345/-/reload
          volumeMounts:
            - name: config
              mountPath: /etc/alloy
          resources:
            requests:
              cpu: 1m
              memory: 5Mi
      dnsPolicy: ClusterFirst
      volumes:
        - name: config
          configMap:
            name: alloy
---
# Source: gateway-addons-helm/charts/opentelemetry-collector/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: otel-collector
  namespace: monitoring
  labels:
    helm.sh/chart: opentelemetry-collector-0.117.3
    app.kubernetes.io/name: opentelemetry-collector
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "0.120.0"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: standalone-collector
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/name: opentelemetry-collector
      app.kubernetes.io/instance: gateway-addons-helm
      component: standalone-collector
  strategy:
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/config: 53f3948607b26e83438039b1f256f0aece7134365837af7b12fc6e34b0844dc1
        
      labels:
        app.kubernetes.io/name: opentelemetry-collector
        app.kubernetes.io/instance: gateway-addons-helm
        component: standalone-collector
        
    spec:
      
      serviceAccountName: otel-collector
      securityContext:
        {}
      containers:
        - name: opentelemetry-collector
          args:
            - --config=/conf/relay.yaml
          securityContext:
            {}
          image: "otel/opentelemetry-collector-contrib:0.121.0"
          imagePullPolicy: IfNotPresent
          ports:
            
            - name: envoy-als
              containerPort: 9000
              protocol: TCP
            - name: jaeger-compact
              containerPort: 6831
              protocol: UDP
            - name: jaeger-grpc
              containerPort: 14250
              protocol: TCP
            - name: jaeger-thrift
              containerPort: 14268
              protocol: TCP
            - name: otlp
              containerPort: 4317
              protocol: TCP
            - name: otlp-http
              containerPort: 4318
              protocol: TCP
            - name: zipkin
              containerPort: 9411
              protocol: TCP
          env:
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
          livenessProbe:
            httpGet:
              path: /
              port: 13133
          readinessProbe:
            httpGet:
              path: /
              port: 13133
          volumeMounts:
            - mountPath: /conf
              name: opentelemetry-collector-configmap
      volumes:
        - name: opentelemetry-collector-configmap
          configMap:
            name: otel-collector
            items:
              - key: relay
                path: relay.yaml
      hostNetwork: false
---
# Source: gateway-addons-helm/charts/prometheus/templates/deploy.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/component: server
    app.kubernetes.io/name: prometheus
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: v2.52.0
    helm.sh/chart: prometheus-25.21.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/part-of: prometheus
  name: prometheus
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app.kubernetes.io/component: server
      app.kubernetes.io/name: prometheus
      app.kubernetes.io/instance: gateway-addons-helm
  replicas: 1
  revisionHistoryLimit: 10
  strategy:
    type: Recreate
    rollingUpdate: null
  template:
    metadata:
      labels:
        app.kubernetes.io/component: server
        app.kubernetes.io/name: prometheus
        app.kubernetes.io/instance: gateway-addons-helm
        app.kubernetes.io/version: v2.52.0
        helm.sh/chart: prometheus-25.21.0
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/part-of: prometheus
    spec:
      enableServiceLinks: true
      serviceAccountName: prometheus
      containers:
        - name: prometheus-server-configmap-reload
          image: "quay.io/prometheus-operator/prometheus-config-reloader:v0.73.2"
          imagePullPolicy: "IfNotPresent"
          args:
            - --watched-dir=/etc/config
            - --reload-url=http://127.0.0.1:9090/-/reload
          volumeMounts:
            - name: config-volume
              mountPath: /etc/config
              readOnly: true

        - name: prometheus-server
          image: "prom/prometheus:v2.52.0"
          imagePullPolicy: "IfNotPresent"
          args:
            - --storage.tsdb.retention.time=15d
            - --config.file=/etc/config/prometheus.yml
            - --storage.tsdb.path=/data
            - --web.console.libraries=/etc/prometheus/console_libraries
            - --web.console.templates=/etc/prometheus/consoles
            - --web.enable-lifecycle
          ports:
            - containerPort: 9090
          readinessProbe:
            httpGet:
              path: /-/ready
              port: 9090
              scheme: HTTP
            initialDelaySeconds: 0
            periodSeconds: 5
            timeoutSeconds: 4
            failureThreshold: 3
            successThreshold: 1
          livenessProbe:
            httpGet:
              path: /-/healthy
              port: 9090
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 10
            failureThreshold: 3
            successThreshold: 1
          volumeMounts:
            - name: config-volume
              mountPath: /etc/config
            - name: storage-volume
              mountPath: /data
              subPath: ""
      dnsPolicy: ClusterFirst
      securityContext:
        fsGroup: 65534
        runAsGroup: 65534
        runAsNonRoot: true
        runAsUser: 65534
      terminationGracePeriodSeconds: 300
      volumes:
        - name: config-volume
          configMap:
            name: prometheus
        - name: storage-volume
          emptyDir:
            {}
---
# Source: gateway-addons-helm/charts/loki/templates/single-binary/statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: loki
  labels:
    helm.sh/chart: loki-4.8.0
    app.kubernetes.io/name: loki
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "2.7.3"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: single-binary
    app.kubernetes.io/part-of: memberlist
spec:
  replicas: 1
  podManagementPolicy: Parallel
  updateStrategy:
    rollingUpdate:
      partition: 0
  serviceName: loki-headless
  revisionHistoryLimit: 10
  
  persistentVolumeClaimRetentionPolicy:
    whenDeleted: Delete
    whenScaled: Delete
  selector:
    matchLabels:
      app.kubernetes.io/name: loki
      app.kubernetes.io/instance: gateway-addons-helm
      app.kubernetes.io/component: single-binary
  template:
    metadata:
      annotations:
        checksum/config: 39a9cea617408d4add363b9ca660a8889e48b866eba2e8c8e4bfc10870b29162
      labels:
        app.kubernetes.io/name: loki
        app.kubernetes.io/instance: gateway-addons-helm
        app.kubernetes.io/component: single-binary
        app.kubernetes.io/part-of: memberlist
    spec:
      serviceAccountName: loki
      automountServiceAccountToken: true
      enableServiceLinks: true
      
      securityContext:
        fsGroup: 10001
        runAsGroup: 10001
        runAsNonRoot: true
        runAsUser: 10001
      terminationGracePeriodSeconds: 30
      containers:
        - name: loki
          image: docker.io/grafana/loki:2.7.3
          imagePullPolicy: IfNotPresent
          args:
            - -config.file=/etc/loki/config/config.yaml
            - -target=all
          ports:
            - name: http-metrics
              containerPort: 3100
              protocol: TCP
            - name: grpc
              containerPort: 9095
              protocol: TCP
            - name: http-memberlist
              containerPort: 7946
              protocol: TCP
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
          readinessProbe:
            httpGet:
              path: /ready
              port: http-metrics
            initialDelaySeconds: 30
            timeoutSeconds: 1
          volumeMounts:
            - name: tmp
              mountPath: /tmp
            - name: config
              mountPath: /etc/loki/config
            - name: runtime-config
              mountPath: /etc/loki/runtime-config
            - name: storage
              mountPath: /var/loki
          resources:
            {}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchLabels:
                  app.kubernetes.io/name: loki
                  app.kubernetes.io/instance: gateway-addons-helm
                  app.kubernetes.io/component: single-binary
              topologyKey: kubernetes.io/hostname
        
      volumes:
        - name: tmp
          emptyDir: {}
        - name: config
          configMap:
            name: loki
        - name: runtime-config
          configMap:
            name: loki-runtime
  volumeClaimTemplates:
    - metadata:
        name: storage
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: "10Gi"
---
# Source: gateway-addons-helm/charts/tempo/templates/statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: tempo
  namespace: monitoring
  labels:
    helm.sh/chart: tempo-1.3.1
    app.kubernetes.io/name: tempo
    app.kubernetes.io/instance: gateway-addons-helm
    app.kubernetes.io/version: "2.1.1"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: tempo
      app.kubernetes.io/instance: gateway-addons-helm
  serviceName: tempo-headless
  template:
    metadata:
      labels:
        app.kubernetes.io/name: tempo
        app.kubernetes.io/instance: gateway-addons-helm
      annotations:
        checksum/config: 0898f7ca87563d700c35d7ea1824cd042cf93fb2f05e254d12a854aa97a5c5e5
    spec:
      serviceAccountName: tempo
      automountServiceAccountToken: true
      containers:
      - args:
        - -config.file=/conf/tempo.yaml
        - -mem-ballast-size-mbs=1024
        image: grafana/tempo:2.1.1
        imagePullPolicy: IfNotPresent
        name: tempo
        ports:
        - containerPort: 3100
          name: prom-metrics
        - containerPort: 6831
          name: jaeger-thrift-c
          protocol: UDP
        - containerPort: 6832
          name: jaeger-thrift-b
          protocol: UDP
        - containerPort: 14268
          name: jaeger-thrift-h
        - containerPort: 14250
          name: jaeger-grpc
        - containerPort: 9411
          name: zipkin
        - containerPort: 55680
          name: otlp-legacy
        - containerPort: 4317
          name: otlp-grpc
        - containerPort: 55681
          name: otlp-httplegacy
        - containerPort: 4318
          name: otlp-http
        - containerPort: 55678
          name: opencensus
        resources:
          {}
        env:
        volumeMounts:
        - mountPath: /conf
          name: tempo-conf
      volumes:
      - configMap:
          name: tempo
        name: tempo-conf
  updateStrategy:
    type:
      RollingUpdate
