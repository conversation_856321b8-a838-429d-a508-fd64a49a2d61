dashboard:
  labels: {}

# Values for Grafana dependency
grafana:
  enabled: true
  fullnameOverride: grafana
  datasources:
    datasources.yaml:
      apiVersion: 1
      datasources:
        - name: Prometheus
          type: prometheus
          url: http://prometheus
  adminPassword: admin
  testFramework:
    enabled: false
  service:
    type: LoadBalancer
  dashboardProviders:
    dashboardproviders.yaml:
      apiVersion: 1
      providers:
        - name: "envoy-gateway"
          orgId: 1
          folder: "envoy-gateway"
          type: file
          disableDeletion: false
          editable: true
          options:
            path: /var/lib/grafana/dashboards/envoy-gateway
  dashboardsConfigMaps:
    envoy-gateway: "grafana-dashboards"

# Values for Prometheus dependency
prometheus:
  enabled: true
  # To simplify the deployment, disable non-essential components
  alertmanager:
    enabled: false
  prometheus-pushgateway:
    enabled: false
  kube-state-metrics:
    enabled: false
    customResourceState:
      enabled: true
      # Add (Cluster)Role permissions to list/watch the customResources defined in the config to rbac.extraRules
      config:
        kind: CustomResourceStateMetrics
        spec:
          resources:
            - groupVersionKind:
                group: gateway.networking.k8s.io
                kind: "Gateway"
                version: "v1"
              metricNamePrefix: gatewayapi_gateway
              labelsFromPath:
                name:
                  - metadata
                  - name
                namespace:
                  - metadata
                  - namespace
              metrics:
                - name: "info"
                  help: "Gateway information"
                  each:
                    type: Info
                    info:
                      labelsFromPath:
                        gatewayclass_name: [ spec, gatewayClassName ]
                - name: "labels"
                  help: "Kubernetes labels converted to Prometheus labels."
                  each:
                    type: Info
                    info:
                      path: [ metadata ]
                      labelsFromPath:
                        "*": [ labels ]
                - name: "created"
                  help: "created timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, creationTimestamp ]
                - name: "deleted"
                  help: "deletion timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, deletionTimestamp ]
                - name: "listener_info"
                  help: "Gateway listener information"
                  each:
                    type: Info
                    info:
                      path: [ spec, listeners ]
                      labelsFromPath:
                        listener_name: [ "name" ]
                        port: [ "port" ]
                        protocol: [ "protocol" ]
                        hostname: [ "hostname" ]
                        tls_mode: [ "tls","mode" ]
                        allowed_routes_namespaces_from: [ "allowedRoutes", "namespaces", "from" ]
                - name: "status"
                  help: "status condition"
                  each:
                    type: Gauge
                    gauge:
                      path: [ status, conditions ]
                      labelsFromPath:
                        type: [ "type" ]
                      valueFrom: [ "status" ]
                - name: "status_listener_attached_routes"
                  help: "Number of attached routes for a listener"
                  each:
                    type: Gauge
                    gauge:
                      path: [ status, listeners ]
                      labelsFromPath:
                        listener_name: [ "name" ]
                      valueFrom: [ "attachedRoutes" ]
                - name: "status_address_info"
                  help: "Gateway address types and values"
                  each:
                    type: Info
                    info:
                      path: [ status, addresses ]
                      labelsFromPath:
                        type: [ "type" ]
                        value: [ "value" ]
            - groupVersionKind:
                group: gateway.networking.k8s.io
                kind: "GatewayClass"
                version: "v1"
              metricNamePrefix: gatewayapi_gatewayclass
              labelsFromPath:
                name:
                  - metadata
                  - name
              metrics:
                - name: "info"
                  help: "GatewayClass information"
                  each:
                    type: Info
                    info:
                      labelsFromPath:
                        controller_name: [ spec, controllerName ]
                - name: "labels"
                  help: "Kubernetes labels converted to Prometheus labels."
                  each:
                    type: Info
                    info:
                      path: [ metadata ]
                      labelsFromPath:
                        "*": [ labels ]
                - name: "created"
                  help: "created timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, creationTimestamp ]
                - name: "deleted"
                  help: "deletion timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, deletionTimestamp ]
                - name: "status"
                  help: "status condition"
                  each:
                    type: Gauge
                    gauge:
                      path: [ status, conditions ]
                      labelsFromPath:
                        type: [ "type" ]
                      valueFrom: [ "status" ]
                - name: "status_supported_features"
                  help: "List of supported features for the GatewayClass"
                  each:
                    type: Info
                    info:
                      path: [ status, supportedFeatures ]
                      labelsFromPath:
                        features: [ ]
            - groupVersionKind:
                group: gateway.networking.k8s.io
                kind: "HTTPRoute"
                version: "v1"
              metricNamePrefix: gatewayapi_httproute
              labelsFromPath:
                name:
                  - metadata
                  - name
                namespace:
                  - metadata
                  - namespace
              metrics:
                - name: "labels"
                  help: "Kubernetes labels converted to Prometheus labels."
                  each:
                    type: Info
                    info:
                      path: [ metadata ]
                      labelsFromPath:
                        "*": [ labels ]
                - name: "created"
                  help: "created timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, creationTimestamp ]
                - name: "deleted"
                  help: "deletion timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, deletionTimestamp ]
                - name: "hostname_info"
                  help: "Hostname information"
                  each:
                    type: Info
                    info:
                      path: [ spec, hostnames ]
                      labelsFromPath:
                        hostname: [ ]
                - name: "parent_info"
                  help: "Parent references that the httproute wants to be attached to"
                  each:
                    type: Info
                    info:
                      path: [ spec, parentRefs ]
                      labelsFromPath:
                        parent_group: [ "group" ]
                        parent_kind: [ "kind" ]
                        parent_name: [ "name" ]
                        parent_namespace: [ "namespace" ]
                        parent_section_name: [ "sectionName" ]
                        parent_port: [ "port" ]
                - name: "status_parent_info"
                  help: "Parent references that the httproute is attached to"
                  each:
                    type: Info
                    info:
                      path: [ status, parents ]
                      labelsFromPath:
                        controller_name: [ "controllerName" ]
                        parent_group: [ "parentRef", "group" ]
                        parent_kind: [ "parentRef", "kind" ]
                        parent_name: [ "parentRef", "name" ]
                        parent_namespace: [ "parentRef", "namespace" ]
                        parent_section_name: [ "parentRef", "sectionName" ]
                        parent_port: [ "parentRef", "port" ]
            - groupVersionKind:
                group: gateway.networking.k8s.io
                kind: "GRPCRoute"
                version: "v1"
              metricNamePrefix: gatewayapi_grpcroute
              labelsFromPath:
                name:
                  - metadata
                  - name
                namespace:
                  - metadata
                  - namespace
              metrics:
                - name: "labels"
                  help: "Kubernetes labels converted to Prometheus labels."
                  each:
                    type: Info
                    info:
                      path: [ metadata ]
                      labelsFromPath:
                        "*": [ labels ]
                - name: "created"
                  help: "created timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, creationTimestamp ]
                - name: "deleted"
                  help: "deletion timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, deletionTimestamp ]
                - name: "hostname_info"
                  help: "Hostname information"
                  each:
                    type: Info
                    info:
                      path: [ spec, hostnames ]
                      labelsFromPath:
                        hostname: [ ]
                - name: "parent_info"
                  help: "Parent references that the grpcroute wants to be attached to"
                  each:
                    type: Info
                    info:
                      path: [ spec, parentRefs ]
                      labelsFromPath:
                        parent_group: [ "group" ]
                        parent_kind: [ "kind" ]
                        parent_name: [ "name" ]
                        parent_namespace: [ "namespace" ]
                        parent_section_name: [ "sectionName" ]
                        parent_port: [ "port" ]
                - name: "status_parent_info"
                  help: "Parent references that the grpcroute is attached to"
                  each:
                    type: Info
                    info:
                      path: [ status, parents ]
                      labelsFromPath:
                        controller_name: [ "controllerName" ]
                        parent_group: [ "parentRef", "group" ]
                        parent_kind: [ "parentRef", "kind" ]
                        parent_name: [ "parentRef", "name" ]
                        parent_namespace: [ "parentRef", "namespace" ]
                        parent_section_name: [ "parentRef", "sectionName" ]
                        parent_port: [ "parentRef", "port" ]
            - groupVersionKind:
                group: gateway.networking.k8s.io
                kind: "TCPRoute"
                version: "v1alpha2"
              metricNamePrefix: gatewayapi_tcproute
              labelsFromPath:
                name:
                  - metadata
                  - name
                namespace:
                  - metadata
                  - namespace
              metrics:
                - name: "labels"
                  help: "Kubernetes labels converted to Prometheus labels."
                  each:
                    type: Info
                    info:
                      path: [ metadata ]
                      labelsFromPath:
                        "*": [ labels ]
                - name: "created"
                  help: "created timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, creationTimestamp ]
                - name: "deleted"
                  help: "deletion timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, deletionTimestamp ]
                - name: "parent_info"
                  help: "Parent references that the tcproute wants to be attached to"
                  each:
                    type: Info
                    info:
                      path: [ spec, parentRefs ]
                      labelsFromPath:
                        parent_group: [ "group" ]
                        parent_kind: [ "kind" ]
                        parent_name: [ "name" ]
                        parent_namespace: [ "namespace" ]
                        parent_section_name: [ "sectionName" ]
                        parent_port: [ "port" ]
                - name: "status_parent_info"
                  help: "Parent references that the tcproute is attached to"
                  each:
                    type: Info
                    info:
                      path: [ status, parents ]
                      labelsFromPath:
                        controller_name: [ "controllerName" ]
                        parent_group: [ "parentRef", "group" ]
                        parent_kind: [ "parentRef", "kind" ]
                        parent_name: [ "parentRef", "name" ]
                        parent_namespace: [ "parentRef", "namespace" ]
                        parent_section_name: [ "parentRef", "sectionName" ]
                        parent_port: [ "parentRef", "port" ]
            - groupVersionKind:
                group: gateway.networking.k8s.io
                kind: "TLSRoute"
                version: "v1alpha2"
              metricNamePrefix: gatewayapi_tlsroute
              labelsFromPath:
                name:
                  - metadata
                  - name
                namespace:
                  - metadata
                  - namespace
              metrics:
                - name: "labels"
                  help: "Kubernetes labels converted to Prometheus labels."
                  each:
                    type: Info
                    info:
                      path: [ metadata ]
                      labelsFromPath:
                        "*": [ labels ]
                - name: "created"
                  help: "created timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, creationTimestamp ]
                - name: "deleted"
                  help: "deletion timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, deletionTimestamp ]
                - name: "hostname_info"
                  help: "Hostname information"
                  each:
                    type: Info
                    info:
                      path: [ spec, hostnames ]
                      labelsFromPath:
                        hostname: [ ]
                - name: "parent_info"
                  help: "Parent references that the tlsroute wants to be attached to"
                  each:
                    type: Info
                    info:
                      path: [ spec, parentRefs ]
                      labelsFromPath:
                        parent_group: [ "group" ]
                        parent_kind: [ "kind" ]
                        parent_name: [ "name" ]
                        parent_namespace: [ "namespace" ]
                        parent_section_name: [ "sectionName" ]
                        parent_port: [ "port" ]
                - name: "status_parent_info"
                  help: "Parent references that the tlsroute is attached to"
                  each:
                    type: Info
                    info:
                      path: [ status, parents ]
                      labelsFromPath:
                        controller_name: [ "controllerName" ]
                        parent_group: [ "parentRef", "group" ]
                        parent_kind: [ "parentRef", "kind" ]
                        parent_name: [ "parentRef", "name" ]
                        parent_namespace: [ "parentRef", "namespace" ]
                        parent_section_name: [ "parentRef", "sectionName" ]
                        parent_port: [ "parentRef", "port" ]
            - groupVersionKind:
                group: gateway.networking.k8s.io
                kind: "UDPRoute"
                version: "v1alpha2"
              metricNamePrefix: gatewayapi_udproute
              labelsFromPath:
                name:
                  - metadata
                  - name
                namespace:
                  - metadata
                  - namespace
              metrics:
                - name: "labels"
                  help: "Kubernetes labels converted to Prometheus labels."
                  each:
                    type: Info
                    info:
                      path: [ metadata ]
                      labelsFromPath:
                        "*": [ labels ]
                - name: "created"
                  help: "created timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, creationTimestamp ]
                - name: "deleted"
                  help: "deletion timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, deletionTimestamp ]
                - name: "parent_info"
                  help: "Parent references that the udproute wants to be attached to"
                  each:
                    type: Info
                    info:
                      path: [ spec, parentRefs ]
                      labelsFromPath:
                        parent_group: [ "group" ]
                        parent_kind: [ "kind" ]
                        parent_name: [ "name" ]
                        parent_namespace: [ "namespace" ]
                        parent_section_name: [ "sectionName" ]
                        parent_port: [ "port" ]
                - name: "status_parent_info"
                  help: "Parent references that the udproute is attached to"
                  each:
                    type: Info
                    info:
                      path: [ status, parents ]
                      labelsFromPath:
                        controller_name: [ "controllerName" ]
                        parent_group: [ "parentRef", "group" ]
                        parent_kind: [ "parentRef", "kind" ]
                        parent_name: [ "parentRef", "name" ]
                        parent_namespace: [ "parentRef", "namespace" ]
                        parent_section_name: [ "parentRef", "sectionName" ]
                        parent_port: [ "parentRef", "port" ]
            - groupVersionKind:
                group: gateway.networking.k8s.io
                kind: "BackendTLSPolicy"
                version: "v1alpha3"
              metricNamePrefix: gatewayapi_backendtlspolicy
              labelsFromPath:
                name:
                  - metadata
                  - name
                namespace:
                  - metadata
                  - namespace
              metrics:
                - name: "labels"
                  help: "Kubernetes labels converted to Prometheus labels."
                  each:
                    type: Info
                    info:
                      path: [ metadata ]
                      labelsFromPath:
                        "*": [ labels ]
                - name: "created"
                  help: "created timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, creationTimestamp ]
                - name: "deleted"
                  help: "deletion timestamp"
                  each:
                    type: Gauge
                    gauge:
                      path: [ metadata, deletionTimestamp ]
                - name: "target_info"
                  help: "Target references that the backendtlspolicy wants to be attached to"
                  each:
                    type: Info
                    info:
                      path: [ spec, targetRef ]
                      labelsFromPath:
                        target_group: [ "group" ]
                        target_kind: [ "kind" ]
                        target_name: [ "name" ]
                        target_namespace: [ "namespace" ]
    rbac:
      extraRules:
        - apiGroups:
            - "gateway.networking.k8s.io"
          resources:
            - gateways
            - gatewayclasses
            - httproutes
            - grpcroutes
            - tcproutes
            - tlsroutes
            - udproutes
            - backendtlspolicies
          verbs:
            - list
            - watch
  prometheus-node-exporter:
    enabled: false
  server:
    fullnameOverride: prometheus
    persistentVolume:
      enabled: false
    readinessProbeInitialDelay: 0
    global:
      # Speed up scraping a bit from the default
      scrape_interval: 15s
    service:
      # use LoadBalancer to expose prometheus
      type: LoadBalancer
    # use dockerhub
    image:
      repository: prom/prometheus
    securityContext: {}

# Values for Fluent-bit dependency
# TODO: remove fluent-bit dependency
fluent-bit:
  enabled: true
  image:
    repository: fluent/fluent-bit # use image from dockerhub
  fullnameOverride: fluent-bit
  testFramework:
    enabled: false
  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "2020"
    prometheus.io/path: /api/v1/metrics/prometheus
    fluentbit.io/exclude: "true"
  ## https://docs.fluentbit.io/manual/administration/configuring-fluent-bit/classic-mode/configuration-file
  config:
    service: |
      [SERVICE]
          Daemon Off
          Flush {{ .Values.flush }}
          Log_Level {{ .Values.logLevel }}
          Parsers_File parsers.conf
          Parsers_File custom_parsers.conf
          HTTP_Server On
          HTTP_Listen 0.0.0.0
          HTTP_Port {{ .Values.metricsPort }}
          Health_Check On

    ## https://docs.fluentbit.io/manual/pipeline/inputs
    inputs: |
      [INPUT]
          Name tail
          Path /var/log/containers/*.log
          multiline.parser docker, cri
          Tag kube.*
          Mem_Buf_Limit 5MB
          Skip_Long_Lines On

    ## https://docs.fluentbit.io/manual/pipeline/filters
    filters: |
      [FILTER]
          Name kubernetes
          Match kube.*
          Merge_Log On
          Keep_Log Off
          K8S-Logging.Parser On
          K8S-Logging.Exclude On

      [FILTER]
          Name grep
          Match kube.*
          Regex $kubernetes['container_name'] ^envoy$

      [FILTER]
          Name parser
          Match kube.*
          Key_Name log
          Parser envoy
          Reserve_Data True

    ## https://docs.fluentbit.io/manual/pipeline/outputs
    outputs: |
      [OUTPUT]
          Name                   loki
          Match                  kube.*
          Host                   loki.monitoring.svc.cluster.local
          Port                   3100
          Labels                 job=fluentbit, app=$kubernetes['labels']['app'], k8s_namespace_name=$kubernetes['namespace_name'], k8s_pod_name=$kubernetes['pod_name'], k8s_container_name=$kubernetes['container_name']

# Values for Loki dependency
loki:
  enabled: true
  # Running a single replica of Loki
  ## https://grafana.com/docs/loki/latest/setup/install/helm/install-monolithic/
  deploymentMode: SingleBinary
  fullnameOverride: loki
  loki:
    auth_enabled: false
    compactorAddress: "loki"
    memberlist: "loki-memberlist"
    commonConfig:
      replication_factor: 1
    storage:
      type: "filesystem"
    rulerConfig:
      storage:
        type: "local"
  test:
    enabled: false
  singleBinary:
    replicas: 1
  read:
    replicas: 0
  backend:
    replicas: 0
  write:
    replicas: 0
  monitoring:
    lokiCanary:
      enabled: false
    selfMonitoring:
      enabled: false
      grafanaAgent:
        installOperator: false
  # Disable gateway.
  gateway:
    enabled: false

# Values for Alloy dependency
alloy:
  enabled: false
  fullnameOverride: alloy
  alloy:
    configMap:
      content: |-
        // Write your Alloy config here:
        logging {
          level = "info"
          format = "logfmt"
        }
        loki.write "alloy" {
          endpoint {
            url = "http://loki.monitoring.svc:3100/loki/api/v1/push"
          }
        }
        // discovery.kubernetes allows you to find scrape targets from Kubernetes resources.
        // It watches cluster state and ensures targets are continually synced with what is currently running in your cluster.
        discovery.kubernetes "pod" {
          role = "pod"
        }

        // discovery.relabel rewrites the label set of the input targets by applying one or more relabeling rules.
        // If no rules are defined, then the input targets are exported as-is.
        discovery.relabel "pod_logs" {
          targets = discovery.kubernetes.pod.targets

          // Label creation - "namespace" field from "__meta_kubernetes_namespace"
          rule {
            source_labels = ["__meta_kubernetes_namespace"]
            action = "replace"
            target_label = "namespace"
          }

          // Label creation - "pod" field from "__meta_kubernetes_pod_name"
          rule {
            source_labels = ["__meta_kubernetes_pod_name"]
            action = "replace"
            target_label = "pod"
          }

          // Label creation - "container" field from "__meta_kubernetes_pod_container_name"
          rule {
            source_labels = ["__meta_kubernetes_pod_container_name"]
            action = "replace"
            target_label = "container"
          }

          // Label creation -  "app" field from "__meta_kubernetes_pod_label_app_kubernetes_io_name"
          rule {
            source_labels = ["__meta_kubernetes_pod_label_app_kubernetes_io_name"]
            action = "replace"
            target_label = "app"
          }

          // Label creation -  "job" field from "__meta_kubernetes_namespace" and "__meta_kubernetes_pod_container_name"
          // Concatenate values __meta_kubernetes_namespace/__meta_kubernetes_pod_container_name
          rule {
            source_labels = ["__meta_kubernetes_namespace", "__meta_kubernetes_pod_container_name"]
            action = "replace"
            target_label = "job"
            separator = "/"
            replacement = "$1"
          }

          // Label creation - "container" field from "__meta_kubernetes_pod_uid" and "__meta_kubernetes_pod_container_name"
          // Concatenate values __meta_kubernetes_pod_uid/__meta_kubernetes_pod_container_name.log
          rule {
            source_labels = ["__meta_kubernetes_pod_uid", "__meta_kubernetes_pod_container_name"]
            action = "replace"
            target_label = "__path__"
            separator = "/"
            replacement = "/var/log/pods/*$1/*.log"
          }

          // Label creation -  "container_runtime" field from "__meta_kubernetes_pod_container_id"
          rule {
            source_labels = ["__meta_kubernetes_pod_container_id"]
            action = "replace"
            target_label = "container_runtime"
            regex = "^(\\S+):\\/\\/.+$"
            replacement = "$1"
          }
        }

        // loki.source.kubernetes tails logs from Kubernetes containers using the Kubernetes API.
        loki.source.kubernetes "pod_logs" {
          targets    = discovery.relabel.pod_logs.output
          forward_to = [loki.process.pod_logs.receiver]
        }
        // loki.process receives log entries from other Loki components, applies one or more processing stages,
        // and forwards the results to the list of receivers in the component’s arguments.
        loki.process "pod_logs" {
          stage.static_labels {
              values = {
                cluster = "envoy-gateway",
              }
          }

          forward_to = [loki.write.alloy.receiver]
        }

# Values for Tempo dependency
tempo:
  enabled: true
  fullnameOverride: tempo
  service:
    type: LoadBalancer

# Values for OpenTelemetry-Collector dependency
opentelemetry-collector:
  enabled: false
  ports:
    envoy-als:
      enabled: true
      containerPort: 9000
      servicePort: 9000
      hostPort: 9000
      protocol: TCP
      appProtocol: grpc
  fullnameOverride: otel-collector
  mode: deployment
  image:
    repository: "otel/opentelemetry-collector-contrib"
    tag: "0.121.0"
  config:
    exporters:
      prometheus:
        endpoint: "[${env:MY_POD_IP}]:19001"
      debug:
        verbosity: detailed
      loki:
        endpoint: "http://loki.monitoring.svc:3100/loki/api/v1/push"
      otlp:
        endpoint: tempo.monitoring.svc:4317
        tls:
          insecure: true
    extensions:
      health_check:
        endpoint: "[${env:MY_POD_IP}]:13133"
    processors:
      attributes:
        actions:
          - action: insert
            key: loki.attribute.labels
            # k8s.pod.name is OpenTelemetry format for Kubernetes Pod name,
            # Loki will convert this to k8s_pod_name label.
            value: k8s.pod.name, k8s.namespace.name
    receivers:
      envoyals:
        endpoint: "[${env:MY_POD_IP}]:9000"
      jaeger:
        protocols:
          grpc:
            endpoint: "[${env:MY_POD_IP}]:14250"
          thrift_http:
            endpoint: "[${env:MY_POD_IP}]:14268"
          thrift_compact:
            endpoint: "[${env:MY_POD_IP}]:6831"
      datadog:
        endpoint: "[${env:MY_POD_IP}]:8126"
      zipkin:
        endpoint: "[${env:MY_POD_IP}]:9411"
      otlp:
        protocols:
          grpc:
            endpoint: "[${env:MY_POD_IP}]:4317"
          http:
            endpoint: "[${env:MY_POD_IP}]:4318"
      prometheus:
        config:
          scrape_configs:
            - job_name: opentelemetry-collector
              scrape_interval: 10s
              static_configs:
                - targets:
                    - "[${env:MY_POD_IP}]:8888"
    service:
      telemetry:
        metrics:
          level: none
          address: null # Disable the deprecated setting
          readers:
            - pull:
                exporter:
                  prometheus:
                    host: "localhost"
                    port: 8888
      extensions:
        - health_check
      pipelines:
        metrics:
          exporters:
            - prometheus
          receivers:
            - datadog
            - otlp
        logs:
          exporters:
            - loki
          processors:
            - attributes
          receivers:
            - otlp
            - envoyals
        traces:
          exporters:
            - otlp
          receivers:
            - datadog
            - otlp
            - zipkin
